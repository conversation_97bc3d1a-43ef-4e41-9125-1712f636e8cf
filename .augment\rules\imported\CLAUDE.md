---
type: "always_apply"
description: "Example description"
---
# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Rules
Always respond in Chinese.

## Project Overview

This is a Ninebot e-commerce web application built with Next.js 14+ using a monorepo architecture. The project consists of two main applications:

- **H5 App** (`apps/h5/`): Mobile-first responsive application using Ant Design Mobile
- **Web App** (`apps/web/`): Desktop web application using Ant Design

Both applications share a common core package (`packages/core/`) containing shared business logic, components, and utilities.

## Development Commands

### Root Level Commands
```bash
# Install dependencies
pnpm i

# Start development servers (both apps)
pnpm dev

# Build all applications
pnpm build

# Run linting across all apps
pnpm lint

# Type checking
pnpm type-check

# Format code
pnpm format

# Clean build artifacts
pnpm clean
```

### App-Specific Commands
```bash
# H5 App (mobile, port 3001)
cd apps/h5 && pnpm dev

# Web App (desktop, port 3000)
cd apps/web && pnpm dev

# Generate GraphQL types for development
cd apps/h5 && pnpm codegen:dev
cd apps/web && pnpm codegen:prod

# Deploy to production
cd apps/h5 && pnpm deploy:master
```

## Architecture

### Monorepo Structure
- **Turborepo**: Build system with caching and pipeline optimization
- **pnpm workspace**: Package management and linking
- **Shared packages**: Common logic in `packages/core/`

### Key Technologies
- **Next.js 14.2.24**: React framework with App Router
- **TypeScript 5.5.4**: Type-safe JavaScript
- **Ant Design**: Desktop UI components (web app)
- **Ant Design Mobile**: Mobile UI components (H5 app)
- **Tailwind CSS**: Utility-first CSS framework
- **GraphQL**: API layer with code generation
- **Redux Toolkit**: State management
- **next-intl**: Internationalization support
- **Redis**: Caching layer with RedisJSON and RedisSearch

### Application Structure
```
apps/
├── h5/           # Mobile-responsive application
│   ├── src/
│   │   ├── app/           # Next.js App Router
│   │   ├── components/    # React components
│   │   ├── businessComponents/  # Business-specific components
│   │   ├── hooks/         # Custom React hooks
│   │   ├── services/      # API services
│   │   ├── store/         # Redux store
│   │   ├── styles/        # Global styles
│   │   ├── types/         # TypeScript definitions
│   │   └── utils/         # Utility functions
├── web/          # Desktop application
│   └── similar structure to h5/
packages/
└── core/         # Shared business logic
    ├── graphql/      # GraphQL queries and mutations
    ├── services/     # API clients
    ├── store/        # Redux configuration
    ├── utils/        # Shared utilities
    └── types/        # Shared TypeScript definitions
```

### Key Patterns
- **Server Components**: Next.js App Router with server/client component separation
- **GraphQL Codegen**: Auto-generated TypeScript types from GraphQL schema
- **Internationalization**: Multi-language support with next-intl
- **Responsive Design**: Mobile-first approach with adaptive layouts
- **Performance**: Build optimization with asset versioning and caching

### Environment Configuration
- Development: `.env.development`
- Production: `.env.production`
- Redis required for caching (RedisJSON and RedisSearch modules)
- Secure image domains configured via `SECURE_IMAGES_DOMAIN`

### State Management
- **Redux Toolkit**: Global state management
- **React Query**: Server state management
- **Local State**: Component state and React hooks

### Styling
- **Tailwind CSS**: Primary styling framework
- **CSS Modules**: Component-scoped styles
- **Ant Design Themes**: Consistent design system
- **PostCSS**: CSS processing and optimizations

### Build and Deployment
- **PM2**: Process management for production
- **Nginx**: Reverse proxy configuration
- **Asset Versioning**: Build ID-based cache busting
- **Source Maps**: Available in development builds

## Development Notes

- Node.js >= 20.16.0 and pnpm >= 9.7.1 required
- Redis server with RedisJSON and RedisSearch modules needed for full functionality
- GraphQL types must be generated when schema changes occur
- Use the provided deploy scripts for production deployment
- Both applications share the same core business logic and API layer
