'use client'

import { useCallback } from 'react'
import { useTranslations } from 'next-intl'
import {
  Address,
  cn,
  mergeStyles,
  ROUTE,
  selectUserAddressById,
  useClipboard,
  useDebounceFn,
  useToastContext,
} from '@ninebot/core'
import { useNavigate } from '@ninebot/core/src/businessHooks'
import { useAppSelector } from '@ninebot/core/src/store/hooks'
import { Card } from 'antd-mobile'

import { CustomButton, IconChecked, IconUncheck } from '@/components'
import { commonStyles } from '@/constants'

const styles = {
  container: 'flex flex-row gap-[8px] items-center justify-center',
  btnText: 'text-[12px] leading-[16px] text-[#0F0F0F] font-miSansRegular330',
  text: 'text-[16px] leading-[1.2] text-[#0F0F0F] font-miSansDemiBold450',
}

type AddressItemProps = {
  id: string
  isEdit: boolean
  updateUserAddress: (address: Address) => Promise<void>
  deleteUserAddress: (addressId: number) => void
}
const AddressItem = ({ id, isEdit, updateUserAddress, deleteUserAddress }: AddressItemProps) => {
  const address = useAppSelector((state) => selectUserAddressById(state, id))

  const getI18nString = useTranslations('Common')
  const toast = useToastContext()
  const { copyToClipboard } = useClipboard()
  const { openPage } = useNavigate()

  /**
   * 设置为默认地址
   */
  const { run: handleSetDefaultAddress } = useDebounceFn(() => {
    if (address?.is_default) {
      toast.show({ content: getI18nString('at_least_one_default_address') })
      return
    }
    const newAddress = { ...address, is_default: true }
    updateUserAddress(newAddress)
  })

  /**
   * 删除地址
   */
  const { run: handleDeleteAddress } = useDebounceFn(() => {
    if (address?.is_default) {
      toast.show({ content: getI18nString('at_least_one_default_address') })
      return
    }
    deleteUserAddress(Number(address?.address_id))
  })

  /**
   * 复制地址内容
   */
  const handleCopyToClipboard = useCallback(() => {
    copyToClipboard(
      `${address.receive_name}，${address.receive_phone}，${address.province} ${address.city} ${address.county} ${address.street}`,
    )
  }, [copyToClipboard, address])

  const renderTitle = useCallback(() => {
    return (
      <div className={cn(commonStyles.flex_start, 'flex-nowrap gap-4')}>
        <div className="max-w-[50%]">
          <span className={cn(styles.text, 'flex-shrink-1 line-clamp-1')}>
            {address.receive_name}
          </span>
        </div>
        <div className={cn(styles.text, 'flex-none')}>{address.receive_phone}</div>
        {address.is_default ? (
          <div className={cn('flex flex-none items-center gap-[4px]')}>
            <span className="rounded-[4px] bg-[#DA291C] px-[4px] py-[1px] font-miSansRegular330 text-[12px] leading-[15.91px] text-[#FFFFFF]">
              默认
            </span>
          </div>
        ) : null}
        {/* <div className="bg-[#FEE5E5] rounded-[4px] text-[12px] leading-[15.91px] text-[#FF3B3B] font-miSansRegular330 py-[1px] px-[4px]">{address.tag}</div> */}
      </div>
    )
  }, [address])

  const renderEdit = useCallback(() => {
    if (!isEdit) return null

    return (
      <div className={mergeStyles([commonStyles.flex_row, 'mt-[8px] h-[24px]'])}>
        <div className={styles.container} onClick={handleSetDefaultAddress}>
          {address.is_default ? (
            <div className="h-[16px] w-[16px]">
              <IconChecked width="100%" height="100%" />
            </div>
          ) : (
            <div className="h-[16px] w-[16px]">
              <IconUncheck width="100%" height="100%" />
            </div>
          )}
          <div className="font-miSansMedium380 text-[14px] leading-[1.2] text-[#444446]">
            {getI18nString('default')}
          </div>
        </div>
        <div className={styles.container}>
          <CustomButton
            className="active:border-[#86868B]"
            customStyle={{ height: 18, width: 36, borderRadius: 4, padding: 0 }}
            fill="outline"
            onClick={handleDeleteAddress}>
            <div className={styles.btnText}>{getI18nString('delete')}</div>
          </CustomButton>
          <CustomButton
            className="active:border-[#86868B]"
            customStyle={{ height: 18, width: 36, borderRadius: 4, padding: 0 }}
            fill="outline"
            onClick={handleCopyToClipboard}>
            <div className={styles.btnText}>{getI18nString('copy')}</div>
          </CustomButton>
          <CustomButton
            className="active:border-[#86868B]"
            customStyle={{ height: 18, width: 36, borderRadius: 4, padding: 0 }}
            fill="outline"
            onClick={() => {
              openPage({
                route: ROUTE.accountAddressEdit,
                value: address.address_id || '',
              })
            }}>
            <div className={styles.btnText}>{getI18nString('edit')}</div>
          </CustomButton>
        </div>
      </div>
    )
  }, [
    address,
    isEdit,
    getI18nString,
    openPage,
    handleSetDefaultAddress,
    handleDeleteAddress,
    handleCopyToClipboard,
  ])

  return (
    <Card
      className="address-item"
      title={renderTitle()}
      headerStyle={{
        paddingBottom: 0,
        paddingTop: 0,
      }}
      style={{
        padding: 0,
        width: '100%',
      }}
      bodyStyle={{
        marginTop: 8,
        paddingBottom: 24,
        borderBottom: '1px solid #F3F3F4',
      }}>
      <div className="line-clamp-2 font-miSansRegular330 text-[14px] leading-[1.4] text-[#6E6E73]">
        {address.province} {address.city} {address.county} {address.street}
      </div>
      {renderEdit()}
    </Card>
  )
}

export default AddressItem
