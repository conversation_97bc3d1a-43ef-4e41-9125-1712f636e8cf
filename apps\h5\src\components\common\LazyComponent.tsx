'use client'
import { useEffect, useRef, useState } from 'react'

interface LazyComponentProps {
  children: React.ReactNode
  placeholder?: React.ReactNode
  rootMargin?: string
  threshold?: number
}

/**
 * 懒加载组件 - 用于优化性能
 */
export const LazyComponent = ({
  children,
  placeholder = <div className="h-32 animate-pulse rounded-lg bg-gray-100" />,
  rootMargin = '100px',
  threshold = 0.1,
}: LazyComponentProps) => {
  const [isVisible, setIsVisible] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!ref.current) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
          observer.disconnect()
        }
      },
      {
        rootMargin,
        threshold,
      },
    )

    observer.observe(ref.current)
    return () => observer.disconnect()
  }, [rootMargin, threshold])

  return <div ref={ref}>{isVisible ? children : placeholder}</div>
}
