'use client'

import { useCallback, useEffect, useMemo } from 'react'

import { TBaseComponentProps } from '../../typings'
import { cn } from '../../utils'
import { IconFail, IconInfo, IconSuccess } from '../icons'

const ICON_TYPE = {
  success: 'success',
  info: 'info',
  fail: 'fail',
} as const

export type TToastProps = TBaseComponentProps<{
  content: string
  visible?: boolean
  duration?: number
  position?: 'center' | 'top'
  icon?: (typeof ICON_TYPE)[keyof typeof ICON_TYPE]
  iconWidth?: number
  iconHeight?: number
  onClose?: () => void
  mask?: boolean
  maskClassName?: string
  containerClassName?: string
  contentClassName?: string
  textClassName?: string
  containerRef?: React.RefObject<HTMLElement>
}>

/**
 * Toast 组件
 */
const Toast = (props: TToastProps) => {
  const {
    content = '',
    visible,
    duration = 3000,
    position = 'top',
    icon,
    iconWidth = 16,
    iconHeight = 16,
    onClose,
    mask = false,
    maskClassName = '',
    containerClassName = '',
    contentClassName = '',
    textClassName = '',
    containerRef,
  } = props

  const ICON_MAP = useMemo(() => {
    return {
      [ICON_TYPE.success]: <IconSuccess color="#FFFFFF" width={iconWidth} height={iconHeight} />,
      [ICON_TYPE.info]: <IconInfo color="#FFFFFF" width={iconWidth} height={iconHeight} />,
      [ICON_TYPE.fail]: <IconFail color="#FFFFFF" width={iconWidth} height={iconHeight} />,
    }
  }, [iconWidth, iconHeight])

  const baseClassName = useMemo(() => {
    return 'z-[10000] bg-[#222223] rounded-[999px] overflow-hidden'
  }, [])

  /**
   * 根据位置设置 Toast 的样式
   */
  const positionClassName = useMemo(() => {
    // 如果指定了容器，使用 absolute 定位相对于容器
    if (containerRef) {
      switch (position) {
        case 'top':
          return 'absolute top-[84px] left-1/2 -translate-x-1/2'
        case 'center':
          return 'absolute right-[227px] top-1/2 translate-x-1/2 -translate-y-1/2'
        default:
          return ''
      }
    }

    // 默认使用 fixed 定位相对于页面
    switch (position) {
      case 'top':
        return 'fixed top-[84px] left-1/2 -translate-x-1/2'
      case 'center':
        return 'fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2'
      default:
        return ''
    }
  }, [position, containerRef])

  /**
   * 关闭事件
   */
  const handleClose = useCallback(() => {
    onClose?.()
  }, [onClose])

  /**
   * 控制 Toast 自动关闭的定时器逻辑
   */
  useEffect(() => {
    let timeOutId = null

    if (visible) {
      timeOutId = setTimeout(handleClose, duration)
    } else {
      if (timeOutId) {
        clearTimeout(timeOutId)
      }
      timeOutId = null
    }

    return () => {
      if (timeOutId) {
        clearTimeout(timeOutId)
      }
    }
  }, [visible, handleClose, duration, mask])

  if (!visible) {
    return null
  }

  return (
    <div>
      {mask && <div className={cn('absolute inset-0 z-[9999] bg-transparent', maskClassName)} />}
      <div className={cn(baseClassName, positionClassName, containerClassName)}>
        <div
          className={cn(
            'inline-flex items-center gap-[4px] overflow-hidden rounded-[99px] bg-black/85 px-[12px] py-[10px]',
            contentClassName,
          )}>
          <span className="flex-none self-center xl:mt-0">
            {ICON_MAP[icon!] ? ICON_MAP[icon!] : null}
          </span>
          <span
            className={cn(
              'line-clamp-3 font-miSansMedium380 text-[14px] leading-[1.2] text-white',
              textClassName,
            )}>
            {content}
          </span>
        </div>
      </div>
    </div>
  )
}

export default Toast
