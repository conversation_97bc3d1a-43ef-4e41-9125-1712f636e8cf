import { HomeProduct, HomeProducts, RelationItem } from '@ninebot/core'

import { BannerSection, CardGrid, ProductCard } from '@/components'

type SpecializedProps = {
  banner: RelationItem
  products: HomeProducts
  currentTime: string
}

export default function Specialized({ banner, products, currentTime }: SpecializedProps) {
  const bannerConfig = {
    subtitle: banner?.subtitle || '',
    title: banner?.title || '',
    description: banner?.description || '',
    buttonText: banner?.button_text || '',
    buttonArrow: banner?.button_arrow || false,
    landingPageUrl: banner?.button_url,
    isVisible: !!banner?.image_url_desktop,
    backgroundImage: banner?.image_url_desktop || '',
  }

  return (
    <div className="max-container-no-mb">
      {banner?.button_url?.type !== 'seckill' &&
        !!banner?.image_url_desktop &&
        bannerConfig.isVisible && <BannerSection {...bannerConfig} />}

      <CardGrid>
        {products?.map((product) => (
          <ProductCard
            key={product?.product?.sku}
            product={product?.product as HomeProduct}
            currentTime={currentTime}
          />
        ))}
      </CardGrid>
    </div>
  )
}
