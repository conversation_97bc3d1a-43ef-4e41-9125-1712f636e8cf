import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslations } from 'next-intl'
import { useAuth, useLazyGetProductLimitQuery, useToastContext } from '@ninebot/core'

import { QuantityButton } from '@/components'
import type { ProductLimitData, ProductStatus } from '@/types/product'

import { useProduct } from '../../context/ProductContext'

const QuantitySelector = () => {
  const { handleQtyChange, productStatus } = useProduct() as {
    handleQtyChange: (value: number, oldValue: number, inputMax: number) => void
    productStatus: ProductStatus
  }
  const [limitCount, setLimitCount] = useState<number | null>(null)
  const [getProductLimit] = useLazyGetProductLimitQuery()
  const { isLoggedIn } = useAuth()
  const toast = useToastContext()
  const getI18nString = useTranslations('Common')

  const selectedProductId = productStatus?.id

  // PDP页面默认最大购买数量限制为99，即使后端返回更大的库存数量
  const salableQty = Math.min(productStatus?.salable_qty || 99, 99)

  /**
   * 最大购买数量
   */
  const inputMax = useMemo(() => {
    return limitCount ? Math.min(limitCount, salableQty) : salableQty
  }, [limitCount, salableQty])

  /**
   * 获取限购数量
   */
  const getLimitCount = useCallback((data: ProductLimitData) => {
    const now = new Date().getTime()
    if (
      data?.limit &&
      now / 1000 >= (data?.start_time || 0) &&
      now / 1000 <= (data?.end_time || 0)
    ) {
      setLimitCount(Number(data?.limit_qty))
    } else {
      setLimitCount(null)
    }
  }, [])

  /**
   * 获取产品限购信息
   */
  useEffect(() => {
    if (isLoggedIn && selectedProductId) {
      getProductLimit({
        input: { product_id: Number(selectedProductId) },
      })
        .unwrap()
        .then((res) => {
          getLimitCount(res?.product_purchase_limit as ProductLimitData)
        })
        .catch((error) => {
          console.log('error', error)
        })
    }
  }, [selectedProductId, getProductLimit, getLimitCount, isLoggedIn])

  /**
   * 处理数量变更
   */
  const handleQuantityChange = useCallback(
    (value: number, oldValue: number) => {
      // 如果尝试超过限购数量，显示 toast 提示
      if (limitCount && value > limitCount && oldValue === limitCount) {
        toast.show({
          icon: 'info',
          content: `${getI18nString('purchase_limit_count_tip_2', { key: limitCount })}`,
        })
        // 保持在限购数量
        handleQtyChange(limitCount, oldValue, inputMax)
      } else {
        handleQtyChange(value, oldValue, inputMax)
      }
    },
    [limitCount, handleQtyChange, inputMax, toast, getI18nString],
  )

  return (
    <QuantityButton
      quantity={productStatus?.quantity || 1}
      onQuantityChange={handleQuantityChange}
      maxQuantity={inputMax}
      isIncrementDisabled={productStatus.isEverythingOutOfStock || productStatus.isOutOfStock}
      ignoreMaxLimit={limitCount !== null} // 仅当有限购限制时启用忽略最大值功能
    />
  )
}

export default QuantitySelector
