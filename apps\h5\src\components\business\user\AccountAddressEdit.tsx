/* eslint-disable @typescript-eslint/no-explicit-any */
'use client'
import { useCallback, useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import {
  selectUserAddressById,
  setUserAllAddress,
  sleep,
  useDebounceFn,
  useLoadingContext,
  useToastContext,
  useUserAddress,
} from '@ninebot/core'
import { CustomerShippingAddressInput } from '@ninebot/core/src/graphql/generated/graphql'
import { useAppDispatch, useAppSelector } from '@ninebot/core/src/store/hooks'
import { Button, Form } from 'antd-mobile'

import { CustomNavBar, IconChecked, IconUncheck } from '@/components'
import { addressValidFields } from '@/constants'
import { useRouter } from '@/i18n/navigation'
import { formAddressDataToShippingAddress } from '@/utils/format'
import { checkMobile } from '@/utils/helper'

import { Choose<PERSON>ity, InputField, MobileField, SelectField } from './components'

const AccountAddressEdit = () => {
  const [form] = Form.useForm()
  const { addressId } = useParams()
  const [cascadeVisible, setCascadeVisible] = useState(false)
  const [addressCascader, setAddressCascader] = useState({
    province: '',
    city: '',
    district: '',
  })
  const [defaultAddress, setDefaultAddress] = useState(false)

  const router = useRouter()
  const toast = useToastContext()
  const dispatch = useAppDispatch()
  const loading = useLoadingContext()
  const getI18nString = useTranslations('Common')

  const address = useAppSelector((state) => selectUserAddressById(state, Number(addressId)))

  const { updateUserAddress, updateUserAddressLoading, userAddressDefault, fetchUserAddresses } =
    useUserAddress()

  /**
   * 默认地址切换事件
   */
  const handleSwitchDefaultAddress = useCallback(() => {
    // 如果没有地址，不允许取消
    if (userAddressDefault && userAddressDefault.address_id === address.address_id) {
      toast.show({
        content: getI18nString('at_least_one_default_address'),
      })
      return
    }

    setDefaultAddress((pre) => !pre)
  }, [userAddressDefault, address, toast, getI18nString])

  /**
   * 更新用户地址
   */
  const { run: onSubmit } = useDebounceFn(async () => {
    const values = form.getFieldsValue()
    const canSubmit = validateFields(values)
    if (canSubmit) {
      const newAddress = formAddressDataToShippingAddress(
        {
          ...values,
          is_default: defaultAddress,
          receive_phone: values?.receive_phone?.replace(/\s/g, ''),
          address_id: address.address_id,
        },
        addressCascader,
      )
      loading.show()
      const result = await updateUserAddress(newAddress as CustomerShippingAddressInput)
      loading.hide()
      if (result) {
        await sleep(500)
        toast.show({ content: getI18nString('update_address_success'), icon: 'success' })

        // 全量更新地址数据
        dispatch(setUserAllAddress(result))

        // 返回上一页
        router.back()
      }
    }
  })

  const validateFields = (values: any) => {
    for (const field of addressValidFields) {
      if (field.key === 'receive_phone') {
        const isValid = checkMobile(values[field.key])
        if (!isValid) {
          toast.show({ content: getI18nString('phone_format_error') })
          return false
        }
      }
      if (
        !values[field.key] ||
        typeof values[field.key] !== 'string' ||
        values[field.key].trim() === ''
      ) {
        toast.show({ content: field.message })
        return false
      }
    }

    return true
  }

  /**
   * loading 处理
   */
  useEffect(() => {
    if (updateUserAddressLoading) {
      loading.show()
    } else {
      loading.hide()
    }
  }, [loading, updateUserAddressLoading])

  /**
   * 回显数据
   */
  useEffect(() => {
    if (address) {
      setAddressCascader({
        province: address.province || '',
        city: address.city || '',
        district: address.county || '',
      })

      form.setFieldsValue({
        area: `${address.province || ''}${address.city || ''}${address.county || ''}`,
        receive_name: address?.receive_name || '',
        receive_phone: address?.receive_phone || '',
        street: address?.street || '',
      })
      setDefaultAddress(address.is_default || false)
    } else {
      // 解决在编辑地址页面直接刷新页面的情况
      fetchUserAddresses()
    }
  }, [address, fetchUserAddresses, form])

  return (
    <>
      <div className="flex flex-1 flex-col bg-gray-base">
        <CustomNavBar title={getI18nString('edit_address')} />
        <div className="my-[12px] flex h-[calc(100vh-198px)] flex-col gap-[8px] px-[12px]">
          <Form form={form} className="address-form" layout="horizontal">
            <Form.Item
              label={getI18nString('consignee')}
              name="receive_name"
              initialValue={address?.receive_name}>
              <InputField placeholder={getI18nString('consignee_tip')} />
            </Form.Item>
            <Form.Item
              label={getI18nString('phone_number')}
              name="receive_phone"
              initialValue={address?.receive_phone}>
              <MobileField />
            </Form.Item>
            <Form.Item
              className="active:!bg-white"
              label={getI18nString('area')}
              name="area"
              onClick={() => setCascadeVisible(true)}
              initialValue={`${address?.province || ''}${address?.city || ''}${address?.county || ''}`}>
              <SelectField placeholder={getI18nString('area_tip')} />
            </Form.Item>
            <Form.Item
              label={getI18nString('detailed_address')}
              name="street"
              initialValue={address?.street}>
              <InputField placeholder={getI18nString('detailed_address_tip')} />
            </Form.Item>
          </Form>
          <div className="flex flex-row items-center justify-between rounded-[8px] bg-white px-[16px] py-8">
            <div className="flex flex-col gap-[6px]">
              <div className="font-miSansDemiBold450 text-[18px] leading-[24px] text-[#0F0F0F]">
                {getI18nString('set_default_address')}
              </div>
              <div className="font-miSansRegular330 text-[14px] leading-[19px] text-[#86868B]">
                {getI18nString('set_default_address_tip')}
              </div>
            </div>
            <button onClick={handleSwitchDefaultAddress}>
              {defaultAddress ? (
                <div className="h-[20px] w-[20px]">
                  <IconChecked width="100%" height="100%" />
                </div>
              ) : (
                <div className="h-[20px] w-[20px]">
                  <IconUncheck width="100%" height="100%" />
                </div>
              )}
            </button>
          </div>
        </div>
        <div className="fixed bottom-0 left-0 right-0 z-10 flex h-[90px] items-center justify-center bg-white px-base-24 py-base-16">
          <Button color="primary" className="nb-button !h-[44px] w-full" onClick={onSubmit}>
            <span className="font-miSansDemiBold450 text-[14px] leading-none text-[#FFFFFF]">
              {getI18nString('confirm')}
            </span>
          </Button>
        </div>
      </div>

      <ChooseCity
        popupVisible={cascadeVisible}
        address={addressCascader}
        selectAddress={setAddressCascader}
        closePopup={() => setCascadeVisible(false)}
      />
    </>
  )
}

export default AccountAddressEdit
