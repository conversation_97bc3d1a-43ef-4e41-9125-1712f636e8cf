'use client'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { usePathname } from 'next/navigation'
import { useTranslations } from 'next-intl'
import {
  mergeStyles,
  resolveCatchMessage,
  storeConfigSelector,
  TCatchMessage,
  TRACK_EVENT,
  useAuth,
  useCheckoutCart,
  useGetCategoriesAllQuery,
  useLazyGetCategoriesAllQuery,
  useLazyGetCustomerHasCarQuery,
  useLazyGetHomeTopNavigationQuery,
  useLoadingContext,
  useScrollToTop,
  useToastContext,
  useVolcAnalytics,
} from '@ninebot/core'
import { HomeFormatedNavigation, HomeFormatedNavigations } from '@ninebot/core'
import { selectCartAllProductsTotal } from '@ninebot/core/src/store'
import { useAppSelector } from '@ninebot/core/src/store/hooks'
import { useDebounceFn } from 'ahooks'
import clsx from 'clsx'

import { Cart, NineBot, Search, SearchModal, User } from '@/components'
import { Link } from '@/i18n/navigation'
import { useRouter } from '@/i18n/navigation'
import { Category } from '@/types/category'
import { formatHomeNavigation } from '@/utils/format'

import { useTabContext } from '../../business/marketing/TabContext'

import CartBadge from './CartBadge'
import MiniCart from './MiniCart'
import NavigationDropdown from './NavigationDropdown'
import ShoppingDropdown from './ShoppingDropdown'
import UserDropdown from './UserDropdown'

export default function Header() {
  const [isMiniCartOpen, setIsMiniCartOpen] = useState(false)
  const [isMiniCartRightAligned, setIsMiniCartRightAligned] = useState(false)
  const { activeTab: currentIdentifier, setActiveTab: setCurrentIdentifier } = useTabContext()
  const router = useRouter()
  const pathName = usePathname()
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const { reportEvent } = useVolcAnalytics()

  // 判断是否为支付中页面
  const isPayingPage = useMemo(() => {
    return ['/checkout/paying', '/checkout/result'].includes(pathName)
  }, [pathName])

  const totalCount = useAppSelector(selectCartAllProductsTotal) || 0
  const { fetchCheckoutCart } = useCheckoutCart()
  const { isLoggedIn, isAuthLoading, redirectLoginUrl } = useAuth()
  const toast = useToastContext()
  const loading = useLoadingContext()
  const storeConfig = useAppSelector(storeConfigSelector)

  // 滚动到顶部Hook
  const { scrollToTop } = useScrollToTop({
    trigger: 'manual', // Header中只使用手动触发
    behavior: 'smooth',
  })
  // 平板设备检测和状态管理
  const [isTabletMode, setIsTabletMode] = useState(false)
  const [tabletActiveDropdown, setTabletActiveDropdown] = useState<string | null>(null)

  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false)
  const [isUserDropdownRightAligned, setIsUserDropdownRightAligned] = useState(false)
  const userDropdownRef = useRef<HTMLDivElement>(null)
  const userButtonRef = useRef<HTMLButtonElement>(null)
  const cartButtonRef = useRef<HTMLButtonElement>(null)
  const cartDropdownRef = useRef<HTMLDivElement>(null)
  const [isShoppingDropdownOpen, setIsShoppingDropdownOpen] = useState(false)

  // 管理导航项悬停下拉菜单的状态
  const [hoveredNavUid, setHoveredNavUid] = useState<string>('')
  const navDropdownRef = useRef<HTMLDivElement>(null)

  // 悬停时的分类数据管理
  const [hoveredCatalogCategory, setHoveredCatalogCategory] = useState<Category>([])
  const [categoryCache, setCategoryCache] = useState<Record<string, Category>>({})
  const [getHoveredCategories, { isLoading: isHoveredCategoriesLoading }] =
    useLazyGetCategoriesAllQuery()
  const fetchTimeoutRef = useRef<NodeJS.Timeout>()
  const closeDropdownTimeoutRef = useRef<NodeJS.Timeout>()

  const getI18nString = useTranslations('Web')
  const getCarRef = useRef(false)
  const [getCustomerHasCar] = useLazyGetCustomerHasCarQuery()
  const [getHomeTopNavigation] = useLazyGetHomeTopNavigationQuery()

  const [isLoading, setIsLoading] = useState(true)
  const [hasCar, setHasCar] = useState(false)
  const [homeNavigation, setHomeNavigation] = useState<HomeFormatedNavigations>([])

  // 购车导航的分类数据（独立获取，不受当前选中导航影响）
  const [shoppingNavCategoryUid, setShoppingNavCategoryUid] = useState('')
  const { data: shoppingCategoriesAll } = useGetCategoriesAllQuery(
    {
      filters: { category_uid: { eq: shoppingNavCategoryUid } },
      pageSize: 9999,
      currentPage: 1,
    },
    { skip: !shoppingNavCategoryUid },
  )
  const [shoppingCatalogCategoryAll, setShoppingCatalogCategoryAll] = useState<Category>([])

  // 处理购车导航的分类数据
  useEffect(() => {
    if (shoppingCategoriesAll?.categories?.items?.length) {
      const newShoppingCatalogCategoryAll =
        shoppingCategoriesAll?.categories?.items?.[0]?.children?.filter(
          (item) => item?.include_in_menu === 1,
        )

      if (newShoppingCatalogCategoryAll) {
        setShoppingCatalogCategoryAll(newShoppingCatalogCategoryAll)
      }
    }
  }, [shoppingCategoriesAll])

  /**
   * 获取悬停时的分类数据
   */
  const fetchHoveredCategoryData = useCallback(
    async (categoryUid: string) => {
      // 清除之前的定时器
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current)
      }

      // 如果缓存中已有数据，立即显示（无延迟）
      if (categoryCache[categoryUid]?.length) {
        setHoveredCatalogCategory(categoryCache[categoryUid])
        return
      }

      // 对于新数据，使用较短的防抖延迟以提升响应性
      fetchTimeoutRef.current = setTimeout(async () => {
        try {
          const result = await getHoveredCategories({
            filters: { category_uid: { eq: categoryUid } },
            pageSize: 9999,
            currentPage: 1,
          }).unwrap()

          if (result?.categories?.items?.[0]?.children) {
            const newCatalogCategory = result.categories.items[0].children.filter(
              (item) => item?.include_in_menu === 1,
            )

            setHoveredCatalogCategory(newCatalogCategory)
            setCategoryCache((prev) => ({
              ...prev,
              [categoryUid]: newCatalogCategory,
            }))
          } else {
            setHoveredCatalogCategory([])
          }
        } catch (error) {
          console.error('Failed to fetch hovered category data:', error)
          setHoveredCatalogCategory([])
        }
      }, 100) // 减少到100ms防抖，提升响应速度
    },
    [getHoveredCategories, categoryCache],
  )

  /**
   * 设置状态
   */
  const handleSetState = useCallback(
    (item: HomeFormatedNavigation) => {
      setCurrentIdentifier(item.value as string)
    },
    [setCurrentIdentifier],
  )

  /**
   * 关闭laoding
   */
  const handleCloseLoading = useCallback(() => {
    setIsLoading(false)
  }, [])

  /**
   * 获取顶部导航
   */
  const getTopNavigation = useCallback(
    async (hadCar = false, identifier = '') => {
      try {
        const res = await getHomeTopNavigation({}).unwrap()
        const topNavigation = res
        if (topNavigation) {
          const { home_top_navigation: homeTopNavigation = [] } = topNavigation
          const newHomeTopNavigation = formatHomeNavigation(hadCar, homeTopNavigation)

          setHomeNavigation(newHomeTopNavigation)

          // 设置购车导航的categoryUid（独立于当前选中的导航）
          const shoppingNav = newHomeTopNavigation?.find((item) => item.showProducts === true)
          if (shoppingNav?.categoryAllUid) {
            setShoppingNavCategoryUid(shoppingNav.categoryAllUid)
          }

          const currentNav = newHomeTopNavigation?.find((item) => item.value === identifier)

          if (newHomeTopNavigation) {
            if (currentNav) {
              handleSetState(currentNav)
            } else {
              handleSetState(newHomeTopNavigation[0])
            }
          }

          handleCloseLoading()

          return newHomeTopNavigation
        }
        return []
      } catch (error) {
        handleCloseLoading()
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error as TCatchMessage) as string,
        })
      }
    },
    [getHomeTopNavigation, handleCloseLoading, handleSetState, toast],
  )

  /**
   * 获取用户是否有车
   */
  const getHasCar = useCallback(
    async (isRefresh = false) => {
      try {
        if (isLoggedIn) {
          const res = await getCustomerHasCar({}).unwrap()
          const hasCarData = res
          const hadCar = hasCarData?.customer?.has_car
          if ((isRefresh && hadCar !== hasCar) || !isRefresh) {
            await getTopNavigation(!!hadCar, currentIdentifier)
          }
          setHasCar(!!hadCar)
        } else {
          setHasCar(false)
          await getTopNavigation(false, currentIdentifier)
        }
      } catch (error) {
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error as TCatchMessage) as string,
        })
      }
    },
    [getCustomerHasCar, getTopNavigation, hasCar, toast, isLoggedIn, currentIdentifier],
  )

  /**
   * 平板设备导航点击处理
   */
  const handleTabletNavClick = useCallback(
    (item: HomeFormatedNavigation, isShoppingNav: boolean) => {
      const navId = isShoppingNav ? 'shopping' : item.categoryAllUid || ''

      if (item.value === currentIdentifier) {
        // 已选中的tab，只控制下拉菜单显示/隐藏
        if (tabletActiveDropdown === navId) {
          setTabletActiveDropdown(null)
          setIsShoppingDropdownOpen(false)
          setHoveredNavUid('')
        } else {
          setTabletActiveDropdown(navId)
          if (isShoppingNav) {
            setIsShoppingDropdownOpen(true)
            setHoveredNavUid('')
          } else if (item.categoryAllUid) {
            setHoveredNavUid(item.categoryAllUid)
            fetchHoveredCategoryData(item.categoryAllUid)
            setIsShoppingDropdownOpen(false)
          }
        }
      } else {
        // 未选中的tab，第一次点击显示下拉菜单
        if (tabletActiveDropdown === navId) {
          // 下拉菜单已显示，切换tab
          handleSetState(item)
          if (pathName !== '/') {
            router.push('/')
          }
          setTabletActiveDropdown(null)
          setIsShoppingDropdownOpen(false)
          setHoveredNavUid('')

          // 滚动到页面顶部
          scrollToTop()
        } else {
          // 显示下拉菜单
          setTabletActiveDropdown(navId)
          if (isShoppingNav) {
            setIsShoppingDropdownOpen(true)
            setHoveredNavUid('')
          } else if (item.categoryAllUid) {
            setHoveredNavUid(item.categoryAllUid)
            fetchHoveredCategoryData(item.categoryAllUid)
            setIsShoppingDropdownOpen(false)
          }
        }
      }
    },
    [
      currentIdentifier,
      tabletActiveDropdown,
      handleSetState,
      pathName,
      router,
      fetchHoveredCategoryData,
      scrollToTop,
    ],
  )

  /**
   * 切换tab
   */
  const handleToggleTab = useCallback(
    (item: HomeFormatedNavigation) => {
      if (isTabletMode) {
        // 平板模式下不直接切换tab，由handleTabletNavClick处理
        return
      }

      handleSetState(item)
      if (pathName !== '/') {
        router.push('/')
      }

      // 关闭所有下拉菜单
      setIsShoppingDropdownOpen(false)
      setHoveredNavUid('')

      // 滚动到页面顶部
      scrollToTop()
    },
    [isTabletMode, handleSetState, pathName, router, scrollToTop],
  )

  /**
   * 平板设备检测
   */
  useEffect(() => {
    const checkTabletMode = () => {
      const width = window.innerWidth
      setIsTabletMode(width >= 768 && width < 1440)
    }

    checkTabletMode()
    window.addEventListener('resize', checkTabletMode)
    return () => window.removeEventListener('resize', checkTabletMode)
  }, [])

  /**
   * 平板模式下点击外部区域关闭下拉菜单
   */
  useEffect(() => {
    if (!isTabletMode || !tabletActiveDropdown) return

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement

      // 检查是否点击在导航区域内
      const navElement = target.closest('[data-nav-shopping], nav')
      if (navElement) return

      // 检查是否点击在下拉菜单内
      const dropdownElement = target.closest('[data-dropdown]')
      if (dropdownElement) return

      // 点击外部区域，关闭下拉菜单
      setTabletActiveDropdown(null)
      setIsShoppingDropdownOpen(false)
      setHoveredNavUid('')
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [isTabletMode, tabletActiveDropdown])

  /**
   * 获取首页配置
   */
  useEffect(() => {
    if (!getCarRef.current && !isAuthLoading) {
      getHasCar()
      getCarRef.current = true
    }
  }, [getHasCar, isAuthLoading])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const targetElement = event.target as Element
      if (
        isUserDropdownOpen &&
        !userDropdownRef.current?.contains(targetElement) &&
        !userButtonRef.current?.contains(targetElement)
      ) {
        setIsUserDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [isUserDropdownOpen])

  // 处理用户下拉菜单与其他下拉菜单的互斥
  useEffect(() => {
    if (isUserDropdownOpen) {
      setHoveredNavUid('')
      setIsMiniCartOpen(false)
      setIsShoppingDropdownOpen(false)
      setIsSearchOpen(false)
    }
  }, [isUserDropdownOpen])

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const targetElement = event.target as Element
      if (
        isMiniCartOpen &&
        !cartDropdownRef.current?.contains(targetElement) &&
        !cartButtonRef.current?.contains(targetElement)
      ) {
        setIsMiniCartOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [isMiniCartOpen])

  // 处理购物车下拉菜单与其他下拉菜单的互斥
  useEffect(() => {
    if (isMiniCartOpen) {
      setHoveredNavUid('')
      setIsUserDropdownOpen(false)
      setIsShoppingDropdownOpen(false)
      setIsSearchOpen(false)
    }
  }, [isMiniCartOpen])

  const handleSearchClose = () => {
    setIsSearchOpen(false)
  }

  // 处理搜索弹窗与其他下拉菜单的互斥
  useEffect(() => {
    if (isSearchOpen) {
      setHoveredNavUid('')
      setIsMiniCartOpen(false)
      setIsUserDropdownOpen(false)
      setIsShoppingDropdownOpen(false)
    }
  }, [isSearchOpen])

  // 处理导航项悬停下拉菜单的点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const targetElement = event.target as Element
      if (hoveredNavUid && !navDropdownRef.current?.contains(targetElement)) {
        setHoveredNavUid('')
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [hoveredNavUid])

  // 处理购车下拉弹窗与其他下拉菜单的互斥
  useEffect(() => {
    if (isShoppingDropdownOpen) {
      setHoveredNavUid('')
      setIsMiniCartOpen(false)
      setIsUserDropdownOpen(false)
      setIsSearchOpen(false)
    }
  }, [isShoppingDropdownOpen])

  // 处理导航项悬停下拉菜单与其他下拉菜单的互斥
  useEffect(() => {
    if (hoveredNavUid) {
      setIsShoppingDropdownOpen(false)
      setIsMiniCartOpen(false)
      setIsUserDropdownOpen(false)
      setIsSearchOpen(false)
    }
  }, [hoveredNavUid])

  // 监听购车下拉弹窗的关闭事件
  useEffect(() => {
    const handleShoppingDropdownClose = () => {
      setIsShoppingDropdownOpen(false)
    }

    window.addEventListener('shopping-dropdown-close', handleShoppingDropdownClose)
    return () => {
      window.removeEventListener('shopping-dropdown-close', handleShoppingDropdownClose)
    }
  }, [])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (closeDropdownTimeoutRef.current) {
        clearTimeout(closeDropdownTimeoutRef.current)
      }
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current)
      }
    }
  }, [])

  // 计算MiniCart对齐方式
  const calculateMiniCartAlignment = useCallback(() => {
    if (!cartButtonRef.current || !isMiniCartOpen) return

    const cartButton = cartButtonRef.current.getBoundingClientRect()
    const windowWidth = window.innerWidth
    const miniCartWidth = 400 // MiniCart的固定宽度
    const safeMargin = 16 // 安全边距

    // 计算MiniCart居中显示时的右边界位置
    const cartButtonCenter = cartButton.left + cartButton.width / 2
    const miniCartRightEdge = cartButtonCenter + miniCartWidth / 2

    // 如果右边界超出窗口宽度（减去安全边距），则使用右对齐
    setIsMiniCartRightAligned(miniCartRightEdge > windowWidth - safeMargin)
  }, [isMiniCartOpen])

  // 计算UserDropdown对齐方式
  const calculateUserDropdownAlignment = useCallback(() => {
    if (!userButtonRef.current || !isUserDropdownOpen) return

    const userButton = userButtonRef.current.getBoundingClientRect()
    const windowWidth = window.innerWidth
    const userDropdownWidth = 96 // UserDropdown的最小宽度
    const safeMargin = 16 // 安全边距

    // 计算UserDropdown居中显示时的右边界位置
    const userButtonCenter = userButton.left + userButton.width / 2
    const userDropdownRightEdge = userButtonCenter + userDropdownWidth / 2

    // 如果右边界超出窗口宽度（减去安全边距），则使用右对齐
    setIsUserDropdownRightAligned(userDropdownRightEdge > windowWidth - safeMargin)
  }, [isUserDropdownOpen])

  // 监听窗口大小变化和MiniCart状态变化
  useEffect(() => {
    calculateMiniCartAlignment()

    const handleResize = () => {
      calculateMiniCartAlignment()
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [calculateMiniCartAlignment])

  // 监听窗口大小变化和UserDropdown状态变化
  useEffect(() => {
    calculateUserDropdownAlignment()

    const handleResize = () => {
      calculateUserDropdownAlignment()
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [calculateUserDropdownAlignment])

  // 加载页面时获取购物车数据
  useEffect(() => {
    if (!isLoggedIn) return

    fetchCheckoutCart()
  }, [fetchCheckoutCart, isLoggedIn])

  useEffect(() => {
    if (isLoading) {
      loading.show()
    } else {
      loading.hide()
    }
  }, [loading, isLoading])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current)
      }
    }
  }, [])

  const { run: handleCartMouseEnter } = useDebounceFn(
    () => {
      if (pathName === '/checkout/cart') {
        return
      }

      // 平板设备上不显示下拉菜单，只在桌面设备上显示
      if (!isTabletMode) {
        reportEvent(TRACK_EVENT.shop_homepage_cart_button_click, {
          button_id: 'shop_my_cart',
        })
        if (totalCount > 0) {
          setIsMiniCartOpen(true)
        }
        // 关闭用户下拉菜单，实现互斥显示
        setIsUserDropdownOpen(false)
      }
    },
    {
      wait: 500,
      leading: true,
    },
  )

  return (
    <>
      <div className="h-[56px]" />

      {/* 统一遮罩层 - 当有下拉菜单打开时显示 */}
      {(hoveredNavUid || isShoppingDropdownOpen) && (
        <div
          className={clsx(
            'fixed inset-0 z-[40] bg-black/40 transition-all duration-500 ease-out',
            hoveredNavUid || isShoppingDropdownOpen ? 'opacity-100' : 'opacity-0',
          )}
          style={{
            backdropFilter: 'blur(10px)',
            WebkitBackdropFilter: 'blur(10px)',
            background: 'linear-gradient(180deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.5) 100%)',
          }}
          onMouseEnter={() => {
            // 关闭所有下拉菜单
            setHoveredNavUid('')
            setIsShoppingDropdownOpen(false)
            if (isTabletMode) {
              setTabletActiveDropdown(null)
            }
          }}
        />
      )}

      <header
        className={`fixed left-0 right-0 top-0 z-[1001] h-[56px] bg-white py-[16px] backdrop-blur-sm transition-all duration-300 ${
          isScrolled ? 'shadow-md' : ''
        }`}>
        <div
          className={`max-container-no-mb flex items-center max-xl:min-w-max ${isPayingPage ? 'justify-between' : 'justify-between'}`}>
          {/* Logo */}
          <Link href="/" className="flex h-[24px] items-center justify-between gap-[6px]">
            <NineBot />
            <span className="font-miSansSemibold520 text-[14px] leading-[120%] text-[#000000]">
              {getI18nString('mall')}
            </span>
            {isPayingPage ? (
              <>
                <span className="font-miSansSemibold520 text-[14px] leading-[120%] text-[#00000015]">
                  |
                </span>
                <span className="font-miSansSemibold520 text-[14px] leading-[120%] text-[#000000]">
                  收银台
                </span>
              </>
            ) : null}
          </Link>

          {/* 导航菜单 */}
          {!isPayingPage && (
            <nav className="flex h-[16px] items-center justify-center">
              {homeNavigation?.map((nav: HomeFormatedNavigation, index: number) => {
                // 检查是否为购车导航（通过show_products字段判断）
                const isShoppingNav = nav.showProducts ?? false
                const isHovered = hoveredNavUid === nav.categoryAllUid

                return (
                  <div
                    key={`${nav.value}-${index}`}
                    className="group relative flex h-full items-center px-[18px] py-0 hover:text-primary"
                    onMouseEnter={() => {
                      if (!isTabletMode) {
                        // 清除延迟关闭定时器
                        if (closeDropdownTimeoutRef.current) {
                          clearTimeout(closeDropdownTimeoutRef.current)
                          closeDropdownTimeoutRef.current = undefined
                        }

                        if (isShoppingNav) {
                          setIsShoppingDropdownOpen(true)
                          setHoveredNavUid('')
                        } else if (nav.categoryAllUid) {
                          setHoveredNavUid(nav.categoryAllUid)
                          fetchHoveredCategoryData(nav.categoryAllUid)
                          setIsShoppingDropdownOpen(false)
                        }
                      }
                    }}
                    onMouseLeave={() => {
                      if (!isTabletMode) {
                        // 延迟关闭下拉菜单，给用户时间移动到下拉菜单区域
                        closeDropdownTimeoutRef.current = setTimeout(() => {
                          setHoveredNavUid('')
                          setIsShoppingDropdownOpen(false)
                        }, 300) // 300ms延迟
                      }
                    }}>
                    <div
                      {...(isShoppingNav ? { 'data-nav-shopping': true } : {})}
                      className={mergeStyles([
                        'inline-flex cursor-pointer items-center font-miSansMedium380 text-[14px] leading-[16px]',
                        nav.value === currentIdentifier ||
                        (isShoppingNav
                          ? isTabletMode
                            ? tabletActiveDropdown === 'shopping'
                            : isShoppingDropdownOpen
                          : isTabletMode
                            ? tabletActiveDropdown === nav.categoryAllUid
                            : isHovered)
                          ? 'text-primary'
                          : 'text-[#000000] group-hover:text-primary',
                      ])}
                      onClick={() => {
                        if (isTabletMode) {
                          handleTabletNavClick(nav, isShoppingNav)
                        } else {
                          handleToggleTab(nav)
                        }
                      }}>
                      {nav.label}
                    </div>

                    {/* 添加一个不可见的延伸区域，连接导航项和下拉菜单，防止鼠标移动时触发onMouseLeave */}
                    {(isShoppingNav ? isShoppingDropdownOpen : isHovered) && !isTabletMode && (
                      <div
                        className="absolute left-0 h-[20px] w-full"
                        style={{
                          bottom: '-20px',
                          zIndex: 45,
                        }}
                      />
                    )}
                  </div>
                )
              })}
            </nav>
          )}

          {/* 购车下拉弹窗 */}
          {!isPayingPage && (
            <ShoppingDropdown
              isOpen={isTabletMode ? tabletActiveDropdown === 'shopping' : isShoppingDropdownOpen}
              categories={shoppingCatalogCategoryAll}
              categoryAllUid={shoppingNavCategoryUid}
              onClose={() => {
                setIsShoppingDropdownOpen(false)
                if (isTabletMode) {
                  setTabletActiveDropdown(null)
                }
              }}
              onMouseEnter={() => {
                // 当鼠标进入购车下拉菜单区域时，清除关闭定时器
                if (closeDropdownTimeoutRef.current) {
                  clearTimeout(closeDropdownTimeoutRef.current)
                  closeDropdownTimeoutRef.current = undefined
                }
              }}
              onMouseLeave={() => {
                // 当鼠标离开购车下拉菜单区域时，关闭下拉菜单
                if (!isTabletMode) {
                  closeDropdownTimeoutRef.current = setTimeout(() => {
                    setIsShoppingDropdownOpen(false)
                  }, 100) // 添加短暂延迟，防止意外关闭
                }
              }}
            />
          )}

          {/* 导航项下拉菜单 */}
          {!isPayingPage && (
            <NavigationDropdown
              ref={navDropdownRef}
              isOpen={
                isTabletMode
                  ? !!hoveredNavUid && tabletActiveDropdown === hoveredNavUid
                  : !!hoveredNavUid
              }
              categories={hoveredCatalogCategory}
              isLoading={isHoveredCategoriesLoading && !categoryCache[hoveredNavUid]?.length}
              onClose={() => {
                setHoveredNavUid('')
                if (isTabletMode) {
                  setTabletActiveDropdown(null)
                }
              }}
              onMouseEnter={() => {
                // 当鼠标进入下拉菜单区域时，清除关闭定时器
                if (closeDropdownTimeoutRef.current) {
                  clearTimeout(closeDropdownTimeoutRef.current)
                  closeDropdownTimeoutRef.current = undefined
                }
              }}
              onMouseLeave={() => {
                // 当鼠标离开下拉菜单区域时，关闭下拉菜单
                if (!isTabletMode) {
                  closeDropdownTimeoutRef.current = setTimeout(() => {
                    setHoveredNavUid('')
                  }, 100) // 添加短暂延迟，防止意外关闭
                }
              }}
            />
          )}

          {/* 右侧工具栏 */}
          <div className="flex h-8 items-center gap-[32px]">
            {!isPayingPage ? (
              <button
                className="inline-flex h-8 w-8 items-center justify-center transition-transform duration-200 hover:scale-110"
                aria-label="Search"
                onClick={() => {
                  reportEvent(TRACK_EVENT.shop_homepage_search_button_click, {
                    button_id: 'shop_search',
                  })
                  setIsSearchOpen(true)
                  // 关闭其他所有弹窗
                  setIsUserDropdownOpen(false)
                  setIsMiniCartOpen(false)
                  setIsShoppingDropdownOpen(false)
                  setHoveredNavUid('')
                }}
                onMouseEnter={() => {
                  setIsUserDropdownOpen(false)
                  setIsMiniCartOpen(false)
                }}>
                <span className="inline-flex items-center justify-center">
                  <Search currentColor="#000000" />
                </span>
              </button>
            ) : null}

            {!isPayingPage ? (
              <div className="relative">
                <button
                  ref={cartButtonRef}
                  className="relative mb-[4px] flex items-center justify-center transition-transform duration-200 hover:scale-110"
                  aria-label="购物车"
                  onClick={() => {
                    if (pathName === '/checkout/cart') {
                      return
                    }

                    reportEvent(TRACK_EVENT.shop_homepage_cart_button_click, {
                      button_id: 'shop_my_cart',
                    })
                    setIsMiniCartOpen(false)
                    router.push('/checkout/cart')
                  }}
                  onMouseEnter={handleCartMouseEnter}>
                  <Cart />
                  {totalCount > 0 && <CartBadge count={totalCount} />}
                </button>

                {/* Mini Cart */}
                <div
                  ref={cartDropdownRef}
                  onMouseLeave={() => setIsMiniCartOpen(false)}
                  className={clsx(
                    'absolute transition-all duration-300 ease-in-out',
                    isMiniCartRightAligned ? 'right-0' : 'left-1/2 -translate-x-1/2',
                    isMiniCartOpen
                      ? 'visible translate-y-base-16 opacity-100'
                      : 'invisible translate-y-0 opacity-0',
                  )}>
                  <MiniCart setIsOpen={setIsMiniCartOpen} totalCount={totalCount} />
                </div>
              </div>
            ) : null}

            <div className="relative h-8 w-8">
              <button
                ref={userButtonRef}
                className="inline-flex h-full w-full items-center justify-center transition-transform duration-200 hover:scale-110"
                onClick={() => {
                  // 平板设备上直接跳转到用户中心
                  if (isTabletMode) {
                    reportEvent(TRACK_EVENT.shop_homepage_profile_button_click, {
                      button_id: 'shop_my_account',
                    })
                    if (isLoggedIn) {
                      router.push('/customer/account')
                    } else {
                      redirectLoginUrl('')
                    }
                  }
                }}
                onMouseEnter={() => {
                  // 平板设备上不显示下拉菜单，只在桌面设备上显示
                  if (!isTabletMode) {
                    reportEvent(TRACK_EVENT.shop_homepage_profile_button_click, {
                      button_id: 'shop_my_account',
                    })
                    setIsUserDropdownOpen(true)
                    // 关闭购物车下拉菜单，实现互斥显示
                    setIsMiniCartOpen(false)
                  }
                }}>
                <User />
              </button>

              {/* 用户下拉菜单 */}
              <div
                ref={userDropdownRef}
                onMouseLeave={() => setIsUserDropdownOpen(false)}
                className={clsx(
                  'absolute transition-all duration-300 ease-in-out',
                  isUserDropdownRightAligned ? 'right-0' : 'left-1/2 -translate-x-1/2',
                  isUserDropdownOpen
                    ? 'visible translate-y-base-16 opacity-100'
                    : 'invisible translate-y-0 opacity-0',
                )}>
                <UserDropdown
                  setIsOpen={setIsUserDropdownOpen}
                  isLoggedIn={isLoggedIn}
                  redirectLoginUrl={redirectLoginUrl}
                  storeConfig={storeConfig}
                />
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* 搜索弹窗 */}
      <SearchModal isOpen={isSearchOpen} onClose={handleSearchClose} />
    </>
  )
}
