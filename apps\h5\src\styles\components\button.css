/* 按钮基础样式 */
:root:root {
  --adm-button-border-radius: 999px;
  --adm-button-text-color: #000000;
  --adm-button-border-color: #f3f3f4;
  --adm-button-background-color: #f3f3f4;
  --adm-font-family: var(--font-family-miSansDemiBold450);
}

.adm-button.nb-button {
  --adm-color-primary: #da291c;
  padding: 1.2rem 2.4rem;
  font-size: 1.4rem;
  line-height: 1.6rem;
}

.adm-button.nb-button.small {
  --adm-font-family: var(--font-family-miSansMedium380);
  padding: 0.8rem 1.6rem;
  font-size: 1.2rem;
  line-height: 1.4rem;
}

.adm-button.nb-button.big {
  padding: 1.6rem 4.8rem;
  font-size: 1.6rem;
  line-height: 2rem;
}

.adm-button.nb-button:active {
  background: #e1e1e4;
  border-color: #e1e1e4;
  color: #000000;
}

.adm-button-primary.nb-button:active {
  background: #c40001;
  border-color: #c40001;
  color: #fff;
}

.choose_track_list .adm-button:active::before {
  opacity: 0;
}

/* 按钮禁用状态 */
/* .adm-button.nb-button.adm-button-disabled {
  background: rgba(0, 0, 0, 0.08);
  border-color: transparent;
  color: #000000;
} */
