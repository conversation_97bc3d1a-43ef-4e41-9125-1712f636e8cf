'use client'

import { usePathname } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { mergeStyles, TRACK_EVENT, useAuth, useVolcAnalytics } from '@ninebot/core'
import { HomeFormatedNavigation } from '@ninebot/core'
import { Tabs } from 'antd-mobile'

import { CartItemCount } from '@/businessComponents'
import {
  // HeaderCategory,
  HeaderLogo,
  HeaderSearch,
  HeaderUser,
} from '@/components/icons'
import { useRouter } from '@/i18n/navigation'
interface HeaderProps {
  isTransparent?: boolean
  headerTabs?: HomeFormatedNavigation[]
  currentIndex?: number
  currentIdentifier?: string
  handleToggleTab?: (item: HomeFormatedNavigation, index: number) => Promise<void> | void
  isLoading?: boolean
  isCheckout?: boolean
}

export default function Header({
  isTransparent = false,
  headerTabs = [],
  currentIndex = 0,
  currentIdentifier = '',
  handleToggleTab,
  isLoading = false,
  isCheckout = false,
}: HeaderProps) {
  const getI18nString = useTranslations('Common')
  const router = useRouter()
  const pathname = usePathname()
  const { reportEvent } = useVolcAnalytics()

  const { redirectLoginUrl, isLoggedIn } = useAuth()

  // 判断是否为首页
  const isHomePage =
    pathname === '/' || /^\/[a-z]{2}$/.test(pathname) || /^\/[a-z]{2}\/$/.test(pathname)

  // 判断是否是个人中心
  const isCustomerPage = pathname === '/customer/account'

  if (isLoading) {
    return null
  }

  return (
    <>
      {/* Header主体 */}
      <header
        className={mergeStyles(
          'transition-colors duration-300',
          isCustomerPage ? 'bg-transparent' : isTransparent ? 'bg-transparent' : 'bg-white',
        )}>
        <div className="px-base-16 py-base">
          <div className="flex items-center justify-between">
            {isHomePage ? (
              <div className="flex items-center gap-base">
                <HeaderLogo color={isTransparent ? 'white' : 'black'} />
                {/* 首页显示Tabs导航 */}
                <Tabs
                  className={mergeStyles(
                    'header-tabs',
                    isTransparent ? 'header-tabs-transparent' : '',
                  )}
                  activeLineMode="auto"
                  activeKey={currentIdentifier}
                  onChange={(value) => {
                    let nextIndex = currentIndex
                    const currentTab = headerTabs.find((item, index) => {
                      if (item.value === value) {
                        nextIndex = index
                        return true
                      }
                    })
                    if (currentTab) {
                      // 埋点：点击分类tab
                      reportEvent(TRACK_EVENT.shop_homepage_tab_click, {
                        category_id: currentTab.value,
                        category_name: currentTab.label,
                      })
                      handleToggleTab?.(currentTab, nextIndex)
                    }
                  }}>
                  {headerTabs.map((tab, index) => (
                    <Tabs.Tab
                      title={tab.label}
                      key={tab.value}
                      className={mergeStyles(
                        index === currentIndex ? 'header-tab-item-active' : 'header-tab-item',
                        isTransparent ? 'text-white' : 'text-black',
                      )}
                    />
                  ))}
                </Tabs>
              </div>
            ) : (
              // 其他页面显示Logo和标题
              <div className="flex items-center gap-base" onClick={() => router.push('/')}>
                <HeaderLogo color={isTransparent ? 'white' : 'black'} isBig={isCheckout} />
                <div
                  className={mergeStyles(
                    'font-miSansSemibold520 leading-[1.2] transition-colors duration-300',
                    isTransparent ? 'text-white' : 'text-black',
                    isCheckout ? 'text-[16px]' : 'text-[14px]',
                  )}>
                  {getI18nString('mall')}
                </div>
              </div>
            )}

            {/* 右侧工具栏 */}
            {isCheckout ? null : (
              <div className="flex h-[24px] items-center justify-center gap-[14px] will-change-transform">
                <button
                  onClick={() => {
                    reportEvent(TRACK_EVENT.shop_homepage_search_button_click, {
                      button_id: 'shop_search',
                    })
                    router.push('/search')
                  }}>
                  <HeaderSearch
                    className={mergeStyles(
                      'transition-colors duration-300',
                      isTransparent ? 'text-white' : 'text-black',
                    )}
                  />
                </button>

                <CartItemCount
                  className={mergeStyles(
                    'transition-colors duration-300',
                    isTransparent ? 'text-white' : 'text-black',
                  )}
                  isLoggedIn={isLoggedIn}
                />

                <button
                  onClick={() => {
                    if (isLoggedIn) {
                      reportEvent(TRACK_EVENT.shop_homepage_profile_button_click, {
                        button_id: 'shop_my_account',
                      })
                      router.push('/customer/account')
                    } else {
                      redirectLoginUrl('')
                    }
                  }}>
                  <HeaderUser
                    className={mergeStyles(
                      'transition-colors duration-300',
                      isTransparent ? 'text-white' : 'text-black',
                    )}
                  />
                </button>

                {/* <button
                onClick={() => {
                  if (isLoggedIn) {
                    // reportEvent(TRACK_EVENT.shop_homepage_profile_button_click, {
                    //   button_id: 'shop_my_account',
                    // })
                    // router.push('/category-all')
                  } else {
                    redirectLoginUrl('')
                  }
                }}>
                <HeaderCategory
                  className={mergeStyles(
                    'transition-colors duration-300',
                    isTransparent ? 'text-white' : 'text-black',
                  )}
                />
              </button> */}
              </div>
            )}
          </div>
        </div>
      </header>
    </>
  )
}
