import { useEffect, useRef, useState } from 'react'
import { RenderHtml } from '@ninebot/core'

import { CustomEmpty } from '@/components'

/**
 * 商品详情 - 优化性能版本
 */
const ProductDetail = ({ content }: { content: string }) => {
  const [isVisible, setIsVisible] = useState(false)
  const [hasRendered, setHasRendered] = useState(false)
  const detailRef = useRef<HTMLDivElement>(null)

  // 使用IntersectionObserver实现懒加载
  useEffect(() => {
    if (!content || !detailRef.current || hasRendered) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
          setHasRendered(true)
          observer.disconnect()
        }
      },
      {
        rootMargin: '100px', // 提前100px加载
        threshold: 0.1,
      },
    )

    observer.observe(detailRef.current)
    return () => observer.disconnect()
  }, [content, hasRendered])

  if (!content) {
    return <CustomEmpty description={'暂无商品详情'} />
  }

  return (
    <div
      ref={detailRef}
      className="my-base flex flex-col gap-base-16 rounded-base-12 bg-white px-base-12 py-base-16 text-base">
      {/* <div className="font-miSansDemiBold450 text-lg">商品详情</div> */}
      <div className="rounded-lg">
        {isVisible ? (
          <RenderHtml content={content} />
        ) : (
          <div className="h-32 animate-pulse rounded-lg bg-gray-100" />
        )}
      </div>
    </div>
  )
}

export default ProductDetail
