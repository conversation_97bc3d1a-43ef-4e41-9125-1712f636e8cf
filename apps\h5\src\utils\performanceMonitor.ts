import { useEffect, useState } from 'react'

/**
 * 性能监控工具
 */

export interface PerformanceMetrics {
  firstContentfulPaint: number
  largestContentfulPaint: number
  firstInputDelay: number
  cumulativeLayoutShift: number
  timeToInteractive: number
}

export class PerformanceMonitor {
  private metrics: Partial<PerformanceMetrics> = {}
  private observers: PerformanceObserver[] = []

  constructor() {
    this.init()
  }

  private init() {
    // 监听FCP
    this.observeFirstContentfulPaint()

    // 监听LCP
    this.observeLargestContentfulPaint()

    // 监听FID
    this.observeFirstInputDelay()

    // 监听CLS
    this.observeCumulativeLayoutShift()

    // 监听TTI
    this.observeTimeToInteractive()
  }

  private observeFirstContentfulPaint() {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      if (entries.length > 0) {
        this.metrics.firstContentfulPaint = entries[0].startTime
        this.reportMetric('FCP', entries[0].startTime)
      }
    })

    observer.observe({ entryTypes: ['paint'] })
    this.observers.push(observer)
  }

  private observeLargestContentfulPaint() {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      if (entries.length > 0) {
        this.metrics.largestContentfulPaint = entries[entries.length - 1].startTime
        this.reportMetric('LCP', entries[entries.length - 1].startTime)
      }
    })

    observer.observe({ entryTypes: ['largest-contentful-paint'] })
    this.observers.push(observer)
  }

  private observeFirstInputDelay() {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      if (entries.length > 0) {
        const entry = entries[0] as PerformanceEntry & { processingStart: number }
        this.metrics.firstInputDelay = entry.processingStart - entry.startTime
        this.reportMetric('FID', this.metrics.firstInputDelay)
      }
    })

    observer.observe({ entryTypes: ['first-input'] })
    this.observers.push(observer)
  }

  private observeCumulativeLayoutShift() {
    let clsValue = 0

    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        const layoutEntry = entry as PerformanceEntry & { hadRecentInput: boolean; value: number }
        if (!layoutEntry.hadRecentInput) {
          clsValue += layoutEntry.value
        }
      }

      this.metrics.cumulativeLayoutShift = clsValue
      this.reportMetric('CLS', clsValue)
    })

    observer.observe({ entryTypes: ['layout-shift'] })
    this.observers.push(observer)
  }

  private observeTimeToInteractive() {
    // 使用简化版本的TTI计算
    setTimeout(() => {
      const navigation = performance.getEntriesByType(
        'navigation',
      )[0] as PerformanceNavigationTiming
      if (navigation) {
        this.metrics.timeToInteractive = navigation.domInteractive - navigation.fetchStart
        this.reportMetric('TTI', this.metrics.timeToInteractive)
      }
    }, 0)
  }

  private reportMetric(name: string, value: number) {
    // 在这里可以上报到监控系统
    console.log(`[Performance] ${name}: ${value.toFixed(2)}ms`)

    // 可以发送到分析服务
    if (typeof window !== 'undefined') {
      const gtag = (window as Window & { gtag?: (...args: unknown[]) => void }).gtag
      if (gtag) {
        gtag('event', 'performance_metric', {
          metric_name: name,
          metric_value: value,
          metric_unit: 'ms',
        })
      }
    }
  }

  public getMetrics(): PerformanceMetrics {
    return this.metrics as PerformanceMetrics
  }

  public destroy() {
    this.observers.forEach((observer) => observer.disconnect())
    this.observers = []
  }
}

// 单例实例
let performanceMonitor: PerformanceMonitor | null = null

export function getPerformanceMonitor(): PerformanceMonitor {
  if (!performanceMonitor) {
    performanceMonitor = new PerformanceMonitor()
  }
  return performanceMonitor
}

// React Hook
export function usePerformanceMonitor() {
  const [metrics, setMetrics] = useState<Partial<PerformanceMetrics>>({})

  useEffect(() => {
    const monitor = getPerformanceMonitor()

    const interval = setInterval(() => {
      setMetrics(monitor.getMetrics())
    }, 1000)

    return () => {
      clearInterval(interval)
      monitor.destroy()
    }
  }, [])

  return metrics
}
