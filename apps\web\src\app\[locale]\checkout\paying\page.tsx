'use client'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import Image from 'next/image'
import { usePathname } from 'next/navigation'
import { useTranslations } from 'next-intl'
import {
  IconPlus,
  NCoinView,
  PaymentMethod,
  Price,
  resolveCatchMessage,
  TCatchMessage,
  useDebounceFn,
  useGetOrderDetailQuery,
  useNavigate,
  useOrderDetail,
  useTimerCoundown,
  useToastContext,
  useUserOrder,
} from '@ninebot/core'
import loadingIcon from '@ninebot/core/src/assets/images/loading-dark.webp'
import { ROUTE } from '@ninebot/core/src/constants'
import { useLazyCheckOrderPayQuery, useLazyGetOrderH5PayQuery } from '@ninebot/core/src/services'
import { formatTimestamp, getTimeDiff } from '@ninebot/core/src/utils'
import { Button } from 'antd'

import { JumpToCustomerService } from '@/businessComponents'
import { Clock } from '@/businessComponents/OrderList/icons'
import { CustomImage, Skeleton } from '@/components'

import { AVAILABLE_PAYMENTS, PAYMENT_CONFIG, PAYMENT_STATUS } from './types'

/**
 * 支付中页面
 * 用于显示支付进行中的状态，并处理支付结果
 */
export default function Page({ searchParams }: { searchParams: { orderId?: string } }) {
  const { orderId } = searchParams

  // 获取订单详情
  const { data: response, isLoading } = useGetOrderDetailQuery(
    {
      filter: {
        nid: { eq: orderId },
      },
    },
    {
      refetchOnMountOrArgChange: true,
    },
  )

  const orderData = response?.customer?.orders?.items?.[0]

  // 处理订单数据
  const { hasNCoin, isUnPaid, totalAmount, nCoin, paymentMethodCode } = useOrderDetail(orderData)

  const getI18nString = useTranslations('Common')
  const toast = useToastContext()
  const { openPage } = useNavigate()
  const pathname = usePathname()

  const [shouldPoll, setShouldPoll] = useState(false)
  const [isDataReady, setIsDataReady] = useState(false)
  const [isPageVisible, setIsPageVisible] = useState(true)

  const pollingCountRef = useRef(0)
  const pollingTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // API 查询hooks
  const [getOrderH5PayQuery] = useLazyGetOrderH5PayQuery()
  const [checkOrderPayQuery] = useLazyCheckOrderPayQuery()

  const [activePayment, setActivePayment] = useState(
    paymentMethodCode || AVAILABLE_PAYMENTS.huifu_alipay,
  )
  const [qrUrl, setQrUrl] = useState<string>('')
  const [loading, setLoading] = useState(false)
  const [qrCodeStatus, setQrCodeStatus] = useState<'initial' | 'loading' | 'success' | 'error'>(
    'initial',
  )
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([])
  const { getAvailablePayments } = useUserOrder()

  const formatTime = useCallback(
    (time: { total: number; days: number; hours: number; minutes: number; seconds: number }) => {
      const { minutes, seconds } = time
      const pad = (num: number) => String(num).padStart(2, '0')
      return [pad(minutes), pad(seconds)]
    },
    [],
  )

  const [currentTime, setCurrentTime] = useState(Date.now() / 1000)

  /**
   * 处理导航到结果页面
   */
  const handleNavigate = useCallback(
    (isSuccess: boolean) => {
      openPage({
        route: ROUTE.checkoutResult,
        queryParams: {
          orderId,
          isSuccess,
        },
        replace: true,
      })
    },
    [orderId, openPage],
  )

  /**
   * 获取二维码
   */
  const fetchQrCode = useCallback(
    async (payCode: string) => {
      if (!orderId) return
      setLoading(true)
      setQrCodeStatus('loading')
      try {
        const response = await getOrderH5PayQuery({ orderId, payCode }).unwrap()
        let url = ''
        if (payCode === AVAILABLE_PAYMENTS.huifu_alipay) {
          url = response?.get_order_h5_pay?.sdkData?.alipayQrCode || ''
        } else if (payCode === AVAILABLE_PAYMENTS.huifu_wechat) {
          url = response?.get_order_h5_pay?.sdkData?.wechatQrCode || ''
        }

        if (!url) {
          console.error('二维码URL为空')
          setQrUrl('')
          setQrCodeStatus('error')
          return ''
        }

        setQrUrl(url)
        setQrCodeStatus('success')
        return url
      } catch (error) {
        console.error('获取二维码失败:', error)

        const errorMessage = resolveCatchMessage(error as TCatchMessage) as string

        let errorContent = '获取支付二维码失败'

        if (errorMessage.includes('network') || errorMessage.includes('timeout')) {
          errorContent = '网络连接错误，请检查您的网络连接后重试'
        }

        toast.show({
          icon: 'fail',
          content: errorContent,
        })

        setQrUrl('')
        setQrCodeStatus('error')
        return ''
      } finally {
        setLoading(false)
      }
    },
    [orderId, getOrderH5PayQuery, toast],
  )

  /**
   * 切换支付方式
   */
  const handleSwitchPayment = useCallback(
    (code: string) => {
      // 如果点击的是当前已选中的支付方式，则不做任何操作
      if (activePayment === code) {
        return
      }
      setActivePayment(code)
      fetchQrCode(code)
    },
    [fetchQrCode, activePayment],
  )

  const clearPolling = useCallback(() => {
    if (pollingTimeoutRef.current) {
      clearTimeout(pollingTimeoutRef.current)
      pollingTimeoutRef.current = null
    }
    pollingCountRef.current = 0
  }, [])

  // 创建防抖的轮询函数
  const { run: debouncedPoll } = useDebounceFn(
    async () => {
      if (!shouldPoll || !isDataReady) {
        clearPolling()
        return
      }

      try {
        const response = await checkOrderPayQuery({
          orderId: orderId || '',
          action: 'query',
        }).unwrap()

        if (response?.check_order_pay) {
          pollingCountRef.current += 1

          if (response.check_order_pay.code === PAYMENT_STATUS.SUCCESS) {
            clearPolling()
            setShouldPoll(false)
            handleNavigate(true)
            return
          }

          if (response.check_order_pay.code === PAYMENT_STATUS.CANCELLED) {
            clearPolling()
            setShouldPoll(false)
            toast.show({
              icon: 'fail',
              content: response.check_order_pay.message || getI18nString('order_cancelled'),
            })
            handleNavigate(false)
            return
          }

          if (response.check_order_pay.code === PAYMENT_STATUS.FAILED) {
            clearPolling()
            setShouldPoll(false)
            toast.show({
              icon: 'fail',
              content: response.check_order_pay.message || getI18nString('pay_failed'),
            })
            handleNavigate(false)
            return
          }

          if (
            pollingCountRef.current >= PAYMENT_CONFIG.MAX_POLLING_COUNT &&
            response.check_order_pay.code === PAYMENT_STATUS.CAN_PAY
          ) {
            clearPolling()
            setShouldPoll(false)
            handleNavigate(false)
            return
          }

          // 继续轮询，但不在这里设置setTimeout，由外部useEffect控制
        }
      } catch (error) {
        clearPolling()
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error as TCatchMessage) as string,
        })
        setShouldPoll(false)
        handleNavigate(false)
      }
    },
    {
      wait: PAYMENT_CONFIG.POLLING_INTERVAL,
      leading: true,
      trailing: false,
    },
  )

  const startPolling = useCallback(() => {
    if (!shouldPoll || !isDataReady) {
      clearPolling()
      return
    }

    // 直接运行防抖函数
    debouncedPoll()

    // 设置定时器进行下一次轮询
    pollingTimeoutRef.current = setTimeout(() => {
      if (shouldPoll && isDataReady) {
        startPolling()
      }
    }, PAYMENT_CONFIG.POLLING_INTERVAL)
  }, [shouldPoll, isDataReady, debouncedPoll, clearPolling])

  const onEnd = useCallback(() => {
    setTimeout(() => {
      clearPolling()
      setShouldPoll(false)
      handleNavigate(false)
    }, 200)
  }, [handleNavigate, clearPolling])

  // 添加useEffect设置当前时间
  useEffect(() => {
    setCurrentTime(Date.now() / 1000)
  }, [])

  const formattedRes = useTimerCoundown(currentTime, currentTime + 180, 1000, onEnd)
  const [minutes, seconds] = formatTime(formattedRes)

  /**
   * 初始化
   */
  useEffect(() => {
    if (!orderId) {
      toast.show({
        icon: 'fail',
        content: getI18nString('invalid_payment_params'),
      })

      handleNavigate(false)
    }
    if (!isDataReady && orderId && orderData && !isLoading) {
      const amount = Number(totalAmount.value)

      const initPayment = async () => {
        try {
          // 创建支付订单
          await checkOrderPayQuery({
            orderId,
            action: 'create',
          })
          // 获取可用支付方式
          const list = await getAvailablePayments(amount)
          setPaymentMethods(list as PaymentMethod[])

          const newPaymentCode =
            paymentMethodCode || (list?.[0]?.code ?? AVAILABLE_PAYMENTS.huifu_alipay)
          setActivePayment(newPaymentCode)

          setQrCodeStatus('loading')
          await fetchQrCode(newPaymentCode)

          setIsDataReady(true)
          setShouldPoll(true)
        } catch (error) {
          console.error('初始化支付失败:', error)

          const errorMessage = resolveCatchMessage(error as TCatchMessage) as string

          let errorContent = errorMessage

          if (errorMessage.includes('network') || errorMessage.includes('timeout')) {
            errorContent = '网络连接错误，请检查您的网络连接后重试'
          } else if (errorMessage.includes('payment') || errorMessage.includes('pay')) {
            errorContent = '支付初始化失败，请稍后再试或选择其他支付方式'
          } else if (errorMessage.includes('order')) {
            errorContent = '订单信息错误，请返回订单页面重新提交'
          }

          toast.show({
            icon: 'fail',
            content: errorContent,
          })
          handleNavigate(false)
        }
      }

      initPayment()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orderId, orderData, isLoading])

  // 监听组件卸载，确保清理轮询
  useEffect(() => {
    // 组件挂载时开始监听
    const handleBeforeUnload = () => {
      clearPolling()
    }

    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      // 清理所有轮询和事件监听
      clearPolling()
      setShouldPoll(false)
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [clearPolling])

  // 监听路由变化只在实际离开页面时清理
  useEffect(() => {
    const currentPath = pathname

    return () => {
      // 只有真正离开当前路径时才清理
      if (pathname !== currentPath) {
        clearPolling()
        setShouldPoll(false)
      }
    }
  }, [pathname, clearPolling])

  // 添加页面可见性监听
  useEffect(() => {
    const handleVisibilityChange = () => {
      const isVisible = document.visibilityState === 'visible'
      setIsPageVisible(isVisible)

      // 当页面重新变为可见时，如果应该轮询且数据已准备好，则重新开始轮询
      if (isVisible && shouldPoll && isDataReady) {
        clearPolling()
        startPolling()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [shouldPoll, isDataReady, clearPolling, startPolling])

  // 修改轮询监听，考虑页面可见性
  useEffect(() => {
    if (shouldPoll && isDataReady && isPageVisible) {
      startPolling()
    } else {
      clearPolling()
    }

    return () => {
      clearPolling()
    }
  }, [shouldPoll, isDataReady, isPageVisible, startPolling, clearPolling])

  /**
   * 订单超时剩余时间
   */
  const minutesOutDiff = useMemo(() => {
    if (orderData?.payment_time_out) {
      const { minutes, seconds } = getTimeDiff(Date.now(), +orderData.payment_time_out * 1000)

      return seconds > 0 ? minutes + 1 : minutes
    }

    return 0
  }, [orderData])

  // 如果还在加载订单数据，显示加载中
  if (isLoading) {
    return <Skeleton style={{ height: '500px' }} />
  }

  // 如果未找到订单或订单已支付，显示错误信息
  if (!orderData || !isUnPaid) {
    return (
      <div className="max-container absolute inset-0 mx-auto grid grid-cols-1 grid-rows-1">
        <div className="text-center">
          <p className="text-lg">{getI18nString('order_not_found')}</p>
          <Button type="primary" className="mt-4" onClick={() => openPage({ route: ROUTE.home })}>
            {getI18nString('back_to_home')}
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-2 grid-rows-1">
      {/* 左侧订单信息 */}
      <div className="flex w-full flex-col justify-center border-r border-[#00000015]">
        <div className="font-miSansSemibold520 text-[24px] leading-[1.2] text-[#0F0F0F]">
          收银台安全支付
        </div>
        <div className="mt-[28px] font-miSansDemiBold450 text-[28px] leading-[1.2] text-black">
          订单提交成功，请扫描右方二维码支付
        </div>

        <div className="mt-[28px] flex flex-col gap-base-16 font-miSansRegular330 text-[14px] leading-[1.4] text-[#6E6E73]">
          <div>
            订单号：<span>{orderData?.number}</span>
          </div>
          <span>下单时间：{formatTimestamp(orderData?.order_date)}</span>
          <div className="flex items-center">
            订单金额：
            {hasNCoin ? (
              <div className="flex items-center">
                <Price
                  price={totalAmount}
                  currencyStyle="text-[14px] leading-[1.4] text-[#6E6E73]"
                  textStyle="text-[14px] leading-[1.4] text-[#6E6E73]"
                  fractionStyle="text-[14px] leading-[1.4] text-[#6E6E73]"
                />
                <div className="mx-[4px]">
                  <IconPlus color="#6E6E73" />
                </div>
                <NCoinView
                  number={nCoin}
                  iconStyle={{ size: 14, background: '#6E6E73' }}
                  textStyle="text-[14px] leading-[none] text-[#6E6E73]"
                />
              </div>
            ) : (
              <Price
                price={totalAmount}
                currencyStyle="text-[14px] leading-[1.2] text-[#6E6E73]"
                textStyle="text-[14px] leading-[1.2] text-[#6E6E73]"
                fractionStyle="text-[14px] leading-[1.2] text-[#6E6E73]"
              />
            )}
          </div>
        </div>

        {minutesOutDiff > 0 ? (
          <div className="mt-[28px] text-base leading-[1.4] text-[#0F0F0F]">
            请于下单后{minutesOutDiff}分钟内完成支付，如有任何问题，请即刻
            <JumpToCustomerService
              udeskParams={{
                type: 'order',
                targetId: '',
              }}>
              <span className="text-[#035FE7] underline">联系我们</span>
            </JumpToCustomerService>
          </div>
        ) : null}

        {/* 支付方式切换 */}
        <div className="mt-[48px] pb-[20px]">
          <div className="mb-[24px] font-miSansDemiBold450 text-[24px] leading-[1.2] text-[#0F0F0F]">
            支付方式
          </div>
          <div className="flex gap-[32px]">
            {paymentMethods.map((method) => (
              <div
                key={method.code ?? ''}
                onClick={() => handleSwitchPayment(method.code ?? '')}
                className={`flex h-[111px] w-[184px] cursor-pointer flex-col items-center justify-center gap-[12px] border-[1px] ${
                  activePayment === method.code
                    ? 'border-[2px] border-[#FA1F1F]'
                    : 'border-[#F3F3F4]'
                } rounded-[12px] transition-all duration-200`}>
                {method.logo && (
                  <CustomImage
                    src={method.logo ?? ''}
                    alt={method.title ?? ''}
                    width={36}
                    height={36}
                  />
                )}
                <span className="font-miSansMedium380 text-[14px] leading-[1.4] text-black">
                  {method.title}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 右侧二维码 */}
      <div className="flex w-full flex-col items-center justify-center">
        <div className="font-miSansSemibold520 text-[24px] leading-[1.2] text-[#0F0F0F]">
          请扫描下方二维码支付 (元)
        </div>
        <div className="mb-[24px] mt-[16px] flex items-center gap-[4px] text-[14px] leading-[17px] text-[#FA1F1F]">
          <Clock color="#FA1F1F" /> 剩余支付时间 {minutes}分{seconds}秒
        </div>
        <div className="mt-base flex h-[240px] w-[240px] items-center justify-center">
          {loading || qrCodeStatus === 'loading' ? (
            <Image
              priority
              src={loadingIcon}
              loading="eager"
              width={60}
              height={4}
              alt="Loading..."
            />
          ) : qrUrl && qrCodeStatus === 'success' ? (
            <CustomImage src={qrUrl} alt="二维码" width={249} height={249} />
          ) : qrCodeStatus === 'error' ? (
            <div className="flex flex-col items-center">
              <span className="mb-2 text-[#888888]">二维码加载失败</span>
              <Button type="primary" size="small" onClick={() => fetchQrCode(activePayment)}>
                重新加载
              </Button>
            </div>
          ) : null}
        </div>
        <div className="mt-[32px] max-w-[240px] text-center text-base leading-[1.4] text-[#0F0F0F]">
          请使用{paymentMethods.find((p) => p.code === activePayment)?.title}
          移动应用程序扫描二维码以完成付款
        </div>
      </div>
    </div>
  )
}
