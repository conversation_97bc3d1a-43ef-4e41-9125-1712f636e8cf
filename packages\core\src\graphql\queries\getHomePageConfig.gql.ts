import { gql } from '../../utils'

/**
 * 获取首页配置信息
 * model_type 模块类型：1:图片和产品,2:图片轮播,3:图片瀑布流,4:固定图标,5:领券
 */
export const GET_HOME_PAGE_CONFIG = gql`
  query getHomePageConfig($identifier: String!, $customAttributesV3Filter: AttributeFilterInput) {
    pageComponents(identifier: $identifier) {
      title
      components {
        id
        title
        model_type
        button_text
        is_display
        display_mode
        banner_display_mode
        url {
          type
          url
          value
        }
        start_at
        end_at
        activity_end_at
        activity_start_at
        relation_items {
          bottom_title
          button_arrow
          button_text
          button_url {
            type
            url
            value
          }
          description
          id
          image_url
          image_url_desktop
          subtitle
          title
          video_url
          fullscreen
          image_ratio_mobile {
            height
            width
          }
          image_ratio_desktop {
            height
            width
          }
        }
        products {
          product {
            __typename
            name
            sku
            image {
              disabled
              label
              url
            }
            url_key
            url_suffix
            paymeng_method
            special_from_date_timestamp
            special_to_date_timestamp
            new_to_date
            stock_status
            price_range {
              maximum_price {
                discount {
                  amount_off
                }
                final_price {
                  currency
                  value
                }
                regular_price {
                  currency
                  value
                }
              }
            }
            custom_attributesV3(filters: $customAttributesV3Filter) {
              items {
                code
                ... on AttributeValue {
                  value
                }
                ... on AttributeSelectedOptions {
                  selected_options {
                    label
                    value
                  }
                }
              }
            }
            ... on ConfigurableProduct {
              variants {
                product {
                  paymeng_method
                  special_from_date_timestamp
                  special_to_date_timestamp
                  new_to_date
                  stock_status
                  price_range {
                    maximum_price {
                      discount {
                        amount_off
                      }
                      final_price {
                        currency
                        value
                      }
                      regular_price {
                        currency
                        value
                      }
                    }
                  }
                }
              }
            }
          }
          url {
            type
            url
            value
          }
        }
        coupon_rules {
          rule_id
          status
          title
          description
          discount
          discount_type
          status_label
          rule_label
        }
      }
    }
    home_top_navigation {
      page_identifier
      relation_category_uid
      title
      top_categories {
        redirect {
          type
          value
          url
        }
        title
      }
    }
  }
`
