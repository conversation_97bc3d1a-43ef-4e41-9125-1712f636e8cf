'use client'
import { use<PERSON>allback, useEffect, useMemo, useRef, useState } from 'react'
import { TRACK_EVENT, useVolcAnalytics } from '@ninebot/core'
import { ImageViewer, PageIndicator, Swiper } from 'antd-mobile'
import type { MultiImageViewerRef } from 'antd-mobile/es/components/image-viewer'
import type { SwiperRef } from 'antd-mobile/es/components/swiper'

import { Video } from '@/businessComponents'
import { VideoRef } from '@/businessComponents/Video'
import { CustomImage } from '@/components/common'
import { Arrow, IconSoundPlay } from '@/components/icons'

import { useProduct } from '../context/ProductContext'

// 定义产品类型
interface ProductType {
  video_url?: string
  video_image?: string
  media_gallery?: Array<{
    url: string
    label: string
  }>
  digital_has_audio?: boolean
}

// 定义产品状态类型
interface ProductStatusType {
  images?: GalleryItem[]
  popVisible?: boolean
  parentSku?: string
  sku?: string
  quantity?: number
  servicePrice?: number
}

interface GalleryItem {
  id: number
  type: 'image' | 'video'
  url: string
  video_url?: string
  label: string
  image_url?: string
  alt?: string
}

export default function ProductGallery({
  setSoundPopVisible,
}: {
  setSoundPopVisible: (visible: boolean) => void
}) {
  const [showImageViewer, setShowImageViewer] = useState(false)
  const [videoClick, setVideoClick] = useState(false)
  const [currentIndex, setCurrentIndex] = useState(0)
  const previousIndexRef = useRef<number>(0) // 用于存储上一个索引
  const imageViewerRef = useRef<MultiImageViewerRef>(null) // 创建 ImageViewer.Multi 的 ref

  const { reportEvent } = useVolcAnalytics()

  const { productDetails: product, productStatus, carouselIndex, carouselRender } = useProduct()

  const typedProduct = product as ProductType
  const typedProductStatus = productStatus as ProductStatusType

  // 创建一个 ref 来存储所有 Video 组件的引用
  const videoRefs = useRef<Record<number, VideoRef | null>>({}) // 使用 Record<number, VideoRef | null>

  // 处理视频数据
  const videoItems = useMemo(() => {
    const arr: GalleryItem[] = []
    if (typedProduct?.video_url) {
      arr.push({
        id: 0,
        type: 'video',
        url: typedProduct.video_image || '',
        video_url: typedProduct.video_url,
        label: '产品视频',
      })
    }
    return arr
  }, [typedProduct])

  // 处理图片数据 - 优化性能，避免重复计算
  const galleryItems = useMemo<GalleryItem[]>(() => {
    const banners = typedProductStatus?.images || []
    return [...videoItems, ...banners].map((item, i) => ({
      ...item,
      id: i + 1,
      type: item.type || 'image',
      url: item.url,
      image_url: item.url,
      label: item.label || `产品图片 ${i + 1}`,
      alt: item.label,
    }))
  }, [typedProductStatus?.images, videoItems])

  // 创建一个ref来引用Swiper组件
  const swiperRef = useRef<SwiperRef>(null)

  // 当变体图片变化时，将轮播图切换到主图位置
  useEffect(() => {
    // 默认选择第一项（如果有视频，第一项就是视频）
    const defaultIndex = 0
    // 切换前暂停之前的视频（如果存在且是视频）
    const prevItem = galleryItems[previousIndexRef.current]
    if (prevItem?.type === 'video') {
      videoRefs.current[previousIndexRef.current]?.pause()
    }
    setCurrentIndex(defaultIndex)
    // 使用swipeTo方法切换到默认位置
    swiperRef.current?.swipeTo(defaultIndex)
    previousIndexRef.current = defaultIndex // 更新上一个索引
  }, [typedProduct?.video_url, typedProduct?.media_gallery, carouselIndex, galleryItems]) // 添加 galleryItems 依赖

  // 仅图片数组(用于图片查看器)
  const images = useMemo(() => galleryItems.map((item) => item.url), [galleryItems])

  // 处理轮播切换 - 使用useCallback优化
  const handleChange = useCallback(
    (index: number) => {
      // 暂停上一个索引对应的视频（如果存在且是视频）
      const prevItem = galleryItems[previousIndexRef.current]
      if (prevItem?.type === 'video') {
        videoRefs.current[previousIndexRef.current]?.pause()
      }
      setCurrentIndex(index)
      previousIndexRef.current = index // 更新上一个索引
    },
    [galleryItems],
  )

  // 处理图片点击 - 使用useCallback优化
  const handleImageClick = useCallback(
    (item: GalleryItem) => {
      setCurrentIndex(galleryItems.indexOf(item))
      setShowImageViewer(true)

      reportEvent(TRACK_EVENT.shop_homepage_banner_picture_click, {
        banner_id: item?.id || '',
        banner_name: item?.label || '',
      })
    },
    [galleryItems, reportEvent],
  )

  // 处理视频点击 - 使用useCallback优化
  const handleVideoClick = useCallback(
    (item: GalleryItem) => {
      setVideoClick(true)
      handleImageClick(item)
    },
    [handleImageClick],
  )

  return (
    <div className="relative aspect-square w-full overflow-hidden bg-gray-base">
      {carouselRender && galleryItems.length > 0 && (
        <Swiper
          ref={swiperRef}
          onIndexChange={handleChange}
          defaultIndex={currentIndex}
          loop={false}
          style={{ height: '100%' }}
          indicator={(total, current) => {
            if (total === 1) {
              return null
            }
            return (
              <PageIndicator
                total={total}
                current={current}
                style={{
                  '--dot-color': 'rgba(0, 0, 0, 0.15)',
                  '--active-dot-color': 'rgba(0, 0, 0, 0.4)',
                  '--dot-size': '6px',
                  '--active-dot-size': '40px',
                  '--dot-border-radius': '4px',
                  '--active-dot-border-radius': '4px',
                  '--dot-spacing': '8px',
                  position: 'absolute',
                  bottom: '10px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  padding: '4px 8px',
                }}
              />
            )
          }}>
          {galleryItems.map((item, index) => (
            <Swiper.Item key={item.id} style={{ height: '100%' }}>
              <div className="relative h-full w-full">
                {item.type === 'image' ? (
                  <CustomImage
                    src={item.url}
                    alt={item.label}
                    width="100%"
                    height="100%"
                    onClick={() => handleImageClick(item)}
                    fit="cover"
                    className="aspect-square h-full w-full object-cover"
                    style={{
                      display: 'block',
                      maxWidth: '100%',
                      maxHeight: '100%',
                    }}
                  />
                ) : (
                  <div className="aspect-square h-full w-full">
                    <Video
                      ref={(ref: VideoRef | null) => {
                        videoRefs.current[index] = ref
                      }} // 将 ref 存储到 videoRefs 中
                      video_url={item.video_url || ''}
                      poster={item.url}
                      onClick={() => handleVideoClick(item)}
                      withMute={true}
                      initialPaused={false}
                      initialMuted={true}
                    />
                  </div>
                )}
              </div>
            </Swiper.Item>
          ))}
        </Swiper>
      )}

      {typedProduct?.digital_has_audio && (
        <div
          onClick={() => setSoundPopVisible(true)}
          className="absolute bottom-[16px] right-[18px] flex h-[56px] w-[105px] justify-center rounded-full bg-white px-[9px] py-[6px]">
          <div className="flex flex-row items-center gap-2">
            <IconSoundPlay />
            <div className="text-base text-black">试听</div>
          </div>
        </div>
      )}

      {/* 图片/视频查看器 */}
      <ImageViewer.Multi
        ref={imageViewerRef}
        classNames={{
          mask: 'multi-mask',
          body: 'multi-body pdp-image-viewer-body',
        }}
        key={currentIndex}
        images={images}
        visible={showImageViewer}
        onIndexChange={(index) => {
          const item = galleryItems[index]
          if (item.type === 'video') {
            setVideoClick(true)
          } else {
            setVideoClick(false)
          }
        }}
        onClose={() => {
          if (!videoClick) {
            setVideoClick(false)
            setShowImageViewer(false)
          }
        }}
        afterClose={() => {
          imageViewerRef.current?.swipeTo(currentIndex)
        }}
        defaultIndex={currentIndex}
        imageRender={(_, { index }) => {
          const item = galleryItems[index]
          if (item.type === 'video') {
            return (
              <div
                className="flex h-full w-full items-center justify-center"
                onClick={() => {
                  setVideoClick(false)
                  setShowImageViewer(false)
                }}>
                <div className="relative aspect-square h-auto w-full max-w-full">
                  <Video
                    video_url={item.video_url || ''}
                    poster={item.url}
                    initialPaused={false}
                    initialMuted={true}
                    withMute={true}
                    onClick={() => {
                      setVideoClick(true)
                    }}
                  />
                </div>
              </div>
            )
          }
        }}
        renderFooter={() => {
          return (
            <div
              onClick={() => {
                setVideoClick(false)
                setShowImageViewer(false)
              }}>
              <Arrow size={24} color="#ffffff" rotate={180} />
            </div>
          )
        }}
      />
    </div>
  )
}
