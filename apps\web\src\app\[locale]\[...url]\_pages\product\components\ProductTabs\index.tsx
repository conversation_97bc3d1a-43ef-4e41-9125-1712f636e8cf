'use client'
import { useCallback, useEffect, useRef, useState } from 'react'
import type { ProductDetailsData } from '@ninebot/core'
import { RenderHtml } from '@ninebot/core'

import AfterSalesService from './AfterSalesService'
import ProductTermsOfSale from './ProductTermsOfSale'
// import ReviewContent from './ReviewContent'
import TabHeader from './TabHeader'

interface ProductTabsProps {
  productData: ProductDetailsData
  termsOfSale: {
    value: string
  }
  // productStatus: {
  //   selectOptionName: string
  //   serviceItem: {
  //     sku: string
  //   }[]
  // }
  onAddToCart?: () => void
  onBuyNow?: () => void
  // 推荐区域的ref，用于滚动监听和定位
  recommendRef?: React.RefObject<HTMLDivElement>
  // 外部传入的吸顶状态
  isSticky?: boolean
  // 是否显示推荐标签
  showRecommendTab?: boolean
}

const ProductTabs = ({
  productData,
  termsOfSale,
  recommendRef: externalRecommendRef,
  isSticky: externalIsSticky = false,
  showRecommendTab = false,
}: ProductTabsProps) => {
  const [activeTab, setActiveTab] = useState<'detail' | 'recommend'>('detail')
  const [isScrolling, setIsScrolling] = useState(false) // 滚动状态管理

  // 使用外部传入的吸顶状态，如果没有则使用内部状态
  const [internalIsSticky, setInternalIsSticky] = useState(false)
  const isSticky = externalIsSticky || internalIsSticky

  const tabsRef = useRef<HTMLDivElement>(null)
  const tabsWrapperRef = useRef<HTMLDivElement>(null)

  // 防抖timer
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null)
  // 滚动状态重置timer
  const scrollingTimerRef = useRef<NodeJS.Timeout | null>(null)

  // 为每个tab内容区域创建ref
  const detailRef = useRef<HTMLDivElement>(null)
  // 内部创建recommendRef，但可以被外部传入的覆盖
  const internalRecommendRef = useRef<HTMLDivElement>(null)
  const recommendRef = externalRecommendRef || internalRecommendRef

  // 平滑滚动函数 - 使用自定义缓动函数实现更自然的过渡
  const smoothScrollTo = useCallback((targetPosition: number, duration: number = 800) => {
    const startPosition = window.scrollY
    const distance = targetPosition - startPosition
    let startTime: number | null = null

    // 缓动函数 - easeInOutCubic
    const easeInOutCubic = (t: number): number => {
      return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2
    }

    const animation = (currentTime: number) => {
      if (startTime === null) startTime = currentTime
      const timeElapsed = currentTime - startTime
      const progress = Math.min(timeElapsed / duration, 1)

      const easedProgress = easeInOutCubic(progress)
      const currentPosition = startPosition + distance * easedProgress

      window.scrollTo(0, currentPosition)

      if (progress < 1) {
        requestAnimationFrame(animation)
      } else {
        // 滚动完成，重置滚动状态
        setIsScrolling(false)
      }
    }

    setIsScrolling(true)
    requestAnimationFrame(animation)
  }, [])

  // 只有在没有外部吸顶状态时才使用内部监听
  useEffect(() => {
    if (externalIsSticky) return // 如果有外部状态，则不使用内部监听

    const observer = new IntersectionObserver(
      ([entry]) => {
        // 修改判断逻辑: 当元素进入视口时(isIntersecting为true)时,设置吸顶
        setInternalIsSticky(entry.isIntersecting)
      },
      {
        threshold: 0,
        // 调整 rootMargin,使观察点在 Tab 区域顶部
        rootMargin: '0px 0px -100% 0px',
      },
    )

    if (tabsRef.current) {
      observer.observe(tabsRef.current)
    }

    return () => observer.disconnect()
  }, [externalIsSticky])

  // 监听内容区域的滚动，自动切换activeTab
  useEffect(() => {
    const updateActiveTab = () => {
      // 如果正在程序化滚动，则不更新activeTab
      if (isScrolling) return

      // 清除之前的防抖timer
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }

      // 获取当前滚动位置
      const scrollTop = window.scrollY

      // 获取各个section的位置信息
      const sections = [
        { id: 'detail', ref: detailRef },
        { id: 'recommend', ref: recommendRef },
      ]

      let newActiveTab: 'detail' | 'recommend' = 'detail'

      // 找到当前应该激活的tab
      for (let i = sections.length - 1; i >= 0; i--) {
        const section = sections[i]
        if (section.ref.current) {
          const sectionTop = section.ref.current.offsetTop

          // 考虑TabHeader的高度偏移
          const headerOffset = 150

          // 如果section的顶部已经进入视口的上半部分，则激活该tab
          if (scrollTop + headerOffset >= sectionTop - 100) {
            newActiveTab = section.id as 'detail' | 'recommend'
            break
          }
        }
      }

      // 使用防抖机制更新activeTab
      if (newActiveTab !== activeTab) {
        debounceTimerRef.current = setTimeout(() => {
          setActiveTab(newActiveTab)
        }, 150) // 增加防抖延迟，减少频繁切换
      }
    }

    // 监听滚动事件
    const handleScroll = () => {
      // 重置滚动状态计时器
      if (scrollingTimerRef.current) {
        clearTimeout(scrollingTimerRef.current)
      }

      // 如果不是程序化滚动，在滚动停止后重置状态
      if (!isScrolling) {
        scrollingTimerRef.current = setTimeout(() => {
          updateActiveTab()
        }, 50)
      } else {
        updateActiveTab()
      }
    }

    // 初始化时执行一次
    updateActiveTab()

    // 添加滚动监听
    window.addEventListener('scroll', handleScroll, { passive: true })

    return () => {
      window.removeEventListener('scroll', handleScroll)
      // 清理防抖timer
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }
      if (scrollingTimerRef.current) {
        clearTimeout(scrollingTimerRef.current)
      }
    }
  }, [activeTab, isScrolling, recommendRef])

  const tabs: Array<{ id: 'detail' | 'recommend'; label: string }> = [
    { id: 'detail', label: '商品详情' },
    ...(showRecommendTab ? [{ id: 'recommend' as const, label: '为你推荐' }] : []),
    // { id: 'review', label: '商品评价' },
  ]

  // 处理标签切换 - 使用改进的平滑滚动
  const handleTabChange = (tab: 'detail' | 'recommend') => {
    // 立即更新activeTab状态
    setActiveTab(tab)

    let targetRef: React.RefObject<HTMLDivElement> | null = null

    switch (tab) {
      case 'detail':
        targetRef = detailRef
        break
      case 'recommend':
        targetRef = recommendRef
        break
      default:
        return
    }

    if (targetRef?.current) {
      // 计算滚动位置，考虑吸顶header的高度
      const headerOffset = 120 // TabHeader高度 + 一些额外间距
      const elementPosition = targetRef.current.offsetTop
      const offsetPosition = Math.max(0, elementPosition - headerOffset)

      // 使用自定义平滑滚动函数，提供更好的过渡效果
      smoothScrollTo(offsetPosition, 600) // 600ms的滚动时间，更加平滑
    }
  }

  const DescriptionData = productData?.description?.html || productData?.mb_description || ''

  return DescriptionData ? (
    <div className="mt-48" ref={tabsRef}>
      <TabHeader
        tabs={tabs}
        activeTab={activeTab}
        isSticky={isSticky}
        onTabChange={handleTabChange}
        wrapperRef={tabsWrapperRef}
      />

      {/* 添加占位空间,防止fixed定位时内容跳动 - 只有在推荐区域时才需要 */}
      {isSticky && activeTab === 'recommend' && (
        <div style={{ height: tabsWrapperRef.current?.offsetHeight || 60 }} />
      )}

      {/* Tab 内容 - 所有内容都显示，通过滚动自动切换tab */}
      <div className="mt-base-64 space-y-base-48">
        {/* 商品详情 */}
        <div
          ref={detailRef}
          data-tab-id="detail"
          className="min-h-[500px] transition-opacity duration-300 ease-in-out"
          // style={{
          //   opacity: activeTab === 'detail' ? 1 : 0.7,
          // }}
        >
          <div className="font-miSansDemiBold450 text-[40px] leading-[1.2]">商品详情</div>
          <div className="pdp-img-description mt-base-48 rounded-[20px] bg-[#E1E1E4] transition-transform duration-300 ease-in-out">
            <RenderHtml center content={DescriptionData} />
          </div>

          {/* 售后服务 - 作为商品详情的一部分 */}
          <div className="mt-base-48">
            <AfterSalesService productData={productData} />
          </div>

          {/* 条款说明 - 作为商品详情的一部分 */}
          {!!termsOfSale?.value && (
            <div className="mt-base-48">
              <ProductTermsOfSale identifier={termsOfSale} />
            </div>
          )}
        </div>

        {/* 商品评价 */}
        {/* <div ref={reviewRef} data-tab-id="review"><ReviewContent /></div> */}
      </div>
    </div>
  ) : null
}

export default ProductTabs
