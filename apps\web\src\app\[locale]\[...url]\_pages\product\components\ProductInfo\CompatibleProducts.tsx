import { useState } from 'react'
import { Dropdown } from 'antd'

import { IconArrow } from '@/components'
interface CompatibleProductsProps {
  products: string
}

const CompatibleProducts: React.FC<CompatibleProductsProps> = ({ products }) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)

  const dropdownItems = {
    items: products.split(',').map((product, index) => ({
      key: index,
      label: (
        <div className="tetx-[#0F0F0F] px-base-12 py-[6px] font-miSansRegular330 text-[14px] leading-none">
          {product}
        </div>
      ),
    })),
  }

  return (
    <div className="flex flex-col gap-base-24">
      <div className="font-miSansSemibold520 text-[18px] leading-none">适配产品:</div>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-8">
          {products
            .split(',')
            .slice(0, 2)
            .map((product, index, array) => (
              <div key={index} className="flex items-center gap-8">
                <div className="font-miSansRegular330 text-[16px] leading-[1.4] text-[#0F0F0F]">
                  {product}
                </div>
                {index < array.length - 1 && (
                  <div className="h-8 w-px border-l border-[#00000026]"></div>
                )}
              </div>
            ))}
        </div>
        {products.split(',').length > 2 && (
          <Dropdown
            menu={dropdownItems}
            overlayClassName="compatible-products-dropdown"
            placement="bottomRight"
            trigger={['click']}
            onOpenChange={setIsDropdownOpen}>
            <div className="flex cursor-pointer items-center px-base-16">
              <IconArrow color="#86868B" size={14} rotate={isDropdownOpen ? 180 : 0} />
            </div>
          </Dropdown>
        )}
      </div>
    </div>
  )
}

export default CompatibleProducts
