'use client'

import { Button } from 'antd'

import { useMediaQuery } from '@/hooks'

import { useProduct } from '../../context/ProductContext'
import QuantitySelector from '../ProductInfo/QuantitySelector'

interface productStatusType {
  isEverythingOutOfStock: boolean
  isOutOfStock: boolean
}

const ProductActionButtons = () => {
  const { onAddToCartBefore, isBuyNow, addCartLoading, buyNowLoading, productStatus } =
    useProduct() as {
      onAddToCartBefore: (quantity: number) => void
      isBuyNow: boolean
      addCartLoading: boolean
      buyNowLoading: boolean
      productStatus: productStatusType
    }

  const getStatusStock = () => {
    return !(productStatus.isEverythingOutOfStock || productStatus.isOutOfStock)
  }

  const responsive = useMediaQuery()

  return (
    <div className="flex w-full items-center gap-base-12">
      {/* 数量选择 */}
      <div>
        <QuantitySelector />
      </div>

      <div className="flex flex-1 items-center gap-base">
        {getStatusStock() ? (
          isBuyNow ? (
            <Button
              onClick={() => onAddToCartBefore(2)}
              disabled={addCartLoading || buyNowLoading}
              size={responsive?.['xll'] ? 'large' : 'middle'}
              type="primary"
              style={{
                flex: 1,
              }}>
              立即购买
            </Button>
          ) : (
            <>
              <Button
                onClick={() => onAddToCartBefore(1)}
                disabled={addCartLoading || buyNowLoading}
                size={responsive?.['xll'] ? 'large' : 'middle'}
                style={{
                  flex: 1,
                }}>
                加入购物车
              </Button>
              <Button
                onClick={() => onAddToCartBefore(2)}
                disabled={addCartLoading || buyNowLoading}
                type="primary"
                size={responsive?.['xll'] ? 'large' : 'middle'}
                style={{
                  flex: 1,
                }}>
                立即购买
              </Button>
            </>
          )
        ) : (
          <Button
            disabled
            size={responsive?.['xll'] ? 'large' : 'middle'}
            style={{
              flex: 1,
            }}>
            售罄
          </Button>
        )}
      </div>
    </div>
  )
}

export default ProductActionButtons
