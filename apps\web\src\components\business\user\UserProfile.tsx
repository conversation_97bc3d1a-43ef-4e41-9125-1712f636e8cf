'use client'
import { useMemo } from 'react'
import { useTranslations } from 'next-intl'
import { cn, Customer, setOrderFilterStatus, storeConfigSelector } from '@ninebot/core'
import { useAppDispatch, useAppSelector } from '@ninebot/core/src/store/hooks'
import { Avatar } from 'antd'
import upperCase from 'lodash-es/upperCase'

import { IconArrow } from '@/components'
import { useRouter } from '@/i18n/navigation'

import { Coin, Comment, Delivery, Email, Payment, Phone, Shipment } from './icons'

const UserProfile = ({ customer }: { customer: Customer }) => {
  const { info } = customer
  const storeConfig = useAppSelector(storeConfigSelector)
  const getI18nString = useTranslations('Web')
  const dispatch = useAppDispatch()
  const router = useRouter()

  const orderStatuses = [
    {
      key: 'pending',
      icon: <Payment />,
      label: getI18nString('unpaid'),
      status: 'pending',
    },
    {
      key: 'awaiting_shipment',
      icon: <Shipment />,
      label: getI18nString('awaiting_shipment'),
      status: 'processing',
    },
    {
      key: 'awaiting_receipt',
      icon: <Delivery />,
      label: getI18nString('awaiting_receipt'),
      status: 'to_be_used_shipped_pending_pickup',
    },
    {
      key: 'completed',
      icon: <Comment />,
      label: getI18nString('completed'),
      status: 'delivered_completed',
    },
  ]

  const handleToOrders = (status: string) => {
    dispatch(setOrderFilterStatus(status))
    router.push('/customer/orders')
  }

  const handleToUserCenter = () => {
    if (storeConfig?.user_center_url) {
      window.open(storeConfig.user_center_url, '_blank')
    }
  }

  const handleToAppDownload = () => {
    if (storeConfig?.app_download_url) {
      window.open(storeConfig.app_download_url, '_blank')
    }
  }

  // 头像--背景色和名字
  const avatar = useMemo(() => {
    const color = ['bg-[#BBBBBD]', 'bg-[#5881FF]', 'bg-[#F5C051]', 'bg-[#F47E00]', 'bg-[#A025F3]']

    let bgColor = color[0]
    let name = ''

    const str = info.name || info.phone

    if (str) {
      let hash = 0
      for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i)
        hash = (hash << 5) - hash + char
        hash |= 0 // 转换为32位整数
      }

      // 确保结果为0-4
      bgColor = color[Math.abs(hash % 5)]
      // ASCII 基础字符集（含字母、数字、常用符号）
      const enRegex = /^[\x00-\x7F]*$/
      // 优先检测英文（纯ASCII字符）
      if (enRegex.test(str)) {
        name = upperCase(str.slice(0, 2))
      } else {
        // 如果不是英文，则使用拼音首字母
        name = str.slice(0, 1)
      }
    }

    return { bgColor, name }
  }, [info])

  return (
    <div className="mb-base-16 rounded-[20px] bg-white p-base-32">
      {/* User Profile Section */}
      <div className="mb-[28px] flex justify-between gap-x-[10px]">
        <div className="flex gap-base-24">
          {info.avatar ? (
            <Avatar className="w-[108px] flex-none" size={108} src={info.avatar} />
          ) : (
            <div
              className={cn(
                'flex h-[108px] w-[108px] flex-none items-center justify-center rounded-full',
                avatar.bgColor,
              )}>
              <h2 className="font-miSansSemiBold520 text-[38px] text-white">{avatar.name}</h2>
            </div>
          )}
          <div className="flex flex-col gap-[10px] 2xl:gap-8">
            <div>
              <h2 className="mb-base line-clamp-2 font-miSansSemibold520 text-[28px] leading-[1.2]">
                {info.name || info.phone}
              </h2>
              <div className="flex flex-wrap gap-y-[4px] xl:gap-[8px] 2xl:flex-nowrap 2xl:gap-base-32 2xl:gap-y-0">
                {info.phone && (
                  <div className="flex items-center gap-base">
                    <Phone />
                    <span className="text-[#6E6E73]">{info.phone}</span>
                  </div>
                )}
                {info.email && (
                  <div className="flex items-center gap-base">
                    <Email />
                    <span className="text-[#6E6E73]">{info.email}</span>
                  </div>
                )}
              </div>
            </div>
            <div
              className="flex cursor-pointer flex-row items-center gap-[4px]"
              onClick={handleToUserCenter}>
              <span className="font-miSansMedium380 text-[12px] leading-[100%] text-[#035FE7]">
                {getI18nString('account_management')}
              </span>
              <IconArrow color="#035FE7" rotate={-90} />
            </div>
          </div>
        </div>
        <div className="flex h-[117px] w-[200px] max-w-[269px] flex-none flex-col justify-between rounded-[12px] bg-[#F8F8F9] px-base-24 py-base-16 2xl:w-[269px]">
          <div>
            <div className="mb-[4px] font-miSansMedium380 text-[14px] leading-[100%]">
              {getI18nString('n_coins')}
            </div>
            <div className="flex h-[34px] items-center gap-4">
              <span className="w-[20px] flex-none">
                <Coin />
              </span>
              <span className="font-miSansSemibold520 text-[28px] leading-[120%] text-[#0F0F0F]">
                {info.nCoin}
              </span>
            </div>
          </div>
          <div
            className="flex cursor-pointer flex-row items-center gap-[4px]"
            onClick={handleToAppDownload}>
            <span className="whitespace-nowrap font-miSansRegular330 text-[12px] leading-[100%] text-[#035FE7]">
              {getI18nString('n_coins_des')}
            </span>
            <span className="w-[16px] flex-none">
              <IconArrow color="#035FE7" rotate={-90} />
            </span>
          </div>
        </div>
      </div>

      {/* Order Status Icons */}
      <div className="grid grid-cols-4 pb-base-12 pt-base-24">
        {orderStatuses.map((status) => (
          <div
            key={status.label}
            className="flex cursor-pointer flex-col items-center gap-base-12"
            onClick={() => handleToOrders(status.status)}>
            <div>{status.icon}</div>
            <div className="text-[20px] leading-[120%]">{status.label}</div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default UserProfile
