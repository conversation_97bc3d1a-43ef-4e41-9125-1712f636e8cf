'use client'

import React, { useCallback, useState } from 'react'
import NextImage from 'next/image'
/**
 * 默认占位符图片路径（使用本地图片资源）
 */
import DEFAULT_PLACEHOLDER from '@public/images/default.png'
import { Image, type ImageProps } from 'antd-mobile'
export interface CustomImageProps extends ImageProps {
  /**
   * 自定义默认图片URL，如果不传则使用全局默认图片
   */
  defaultSrc?: string
  /**
   * 是否启用默认图片功能，默认为true
   */
  enableDefaultImage?: boolean
  /**
   * 是否在加载时也显示默认图片作为占位符，默认为true
   */
  showDefaultOnLoading?: boolean
  /**
   * 图片加载失败时的回调
   */
  onError?: (event: React.SyntheticEvent<HTMLImageElement, Event>) => void
}

/**
 * 自定义Image组件
 *
 * 基于antd-mobile的Image组件封装，增加了默认图片功能
 * 当图片加载失败时，会自动显示默认图片
 *
 * @param props - 组件属性
 * @returns React组件
 */
const CustomImage: React.FC<CustomImageProps> = ({
  src,
  defaultSrc = DEFAULT_PLACEHOLDER.src,
  enableDefaultImage = true,
  showDefaultOnLoading = true,
  onError,
  ...rest
}) => {
  const [currentSrc, setCurrentSrc] = useState<string>(src || '')
  const [hasError, setHasError] = useState<boolean>(false)

  const handleError = useCallback(
    (event: React.SyntheticEvent<HTMLImageElement, Event>) => {
      // 如果启用默认图片功能且当前不是默认图片且还没有出错过
      if (enableDefaultImage && currentSrc !== defaultSrc && !hasError) {
        setCurrentSrc(defaultSrc)
        setHasError(true)
      }

      // 调用外部传入的onError回调
      onError?.(event)
    },
    [enableDefaultImage, currentSrc, defaultSrc, hasError, onError],
  )

  // 当src属性变化时，重置状态
  React.useEffect(() => {
    if (src !== currentSrc && !hasError) {
      setCurrentSrc(src || '')
      setHasError(false)
    }
  }, [src, currentSrc, hasError])

  return (
    <Image
      {...rest}
      alt={rest.alt || ''}
      src={currentSrc}
      onError={handleError}
      placeholder={
        enableDefaultImage && showDefaultOnLoading ? (
          <div style={{ width: '100%', height: '100%', position: 'relative', overflow: 'hidden' }}>
            <NextImage
              src={defaultSrc}
              alt="占位图片"
              width={400}
              height={400}
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
              }}
              priority={false}
              unoptimized={true}
            />
          </div>
        ) : (
          rest.placeholder
        )
      }
    />
  )
}

export default CustomImage
