import { createSelector, createSlice, PayloadAction } from '@reduxjs/toolkit'
import Big from 'big.js'
import { isNull, isNumber, isUndefined } from 'lodash-es'

import { DELIVERY_METHOD_TEXT, DELIVERY_METHOD_TYPE, ONLY_N_COIN } from '../../../constants'
import { GetCustomerCartQuery, InvoiceInput } from '../../../graphql/generated/graphql'
import { formatProducts } from '../../../utils'
import { RootState } from '../../store'
import { selectUserAddressById } from '../user/userAddressSlice'

export type TCheckoutCustomerCart = NonNullable<GetCustomerCartQuery['customerCart']> & {
  invoice?: InvoiceInput
}
export type TShippingMethods = NonNullable<TCheckoutCustomerCart['allShippingMethods']>
export type TPaymentMethods = NonNullable<TCheckoutCustomerCart['available_payment_methods']>
export type TPrices = NonNullable<TCheckoutCustomerCart['prices']>
export type TCouponsDiscount = NonNullable<TPrices['discounts']>
export type TCheckoutCartProductItems = NonNullable<
  NonNullable<TCheckoutCustomerCart['itemsV2']>['items']
>
export type TCheckoutCartProductItem = NonNullable<TCheckoutCartProductItems[number]>
export type TCheckoutNCoinPayTotal = TCheckoutCustomerCart['ncoin_pay']

export type TCheckoutSliceState = {
  // 购物车 ID
  cartId: string
  // 本单最大使用 N币 数量
  maxNCoinUsed: number
  // 用户 N币 信息
  userNCoinInfo: {
    // 用户 N币 余额
    totalBalance: number
    totalToExpire: number
  }
  // 购买自提商品 + 虚拟商品或只单独购买自提，返回 true
  isPickup: boolean
  // 只单独购买虚拟商品，返回 true
  isVirtual: boolean
  // 是否全是纯N币产品
  isNCoinPay: boolean
  // 纯N币产品支付总价
  NCoinPayTotal: TCheckoutNCoinPayTotal | null
  // 配送方式
  shippingMethods: TShippingMethods
  // 支付方式
  paymentMethods: TPaymentMethods
  // 价格明细
  prices: TPrices
  // 产品数据
  products: TCheckoutCartProductItems
  // 保险产品 ID
  insuranceItemId: string
  // 优惠券折扣信息
  couponsDiscount: TCouponsDiscount
  // 下单数据
  placeOrderData: {
    addressId: string | null
    shippingMethodCode: string | null
    paymentMethodCode: string | null
  }
  // 是否初始化完结算页流程
  initProcessComplete: boolean
  invoice?: InvoiceInput
}

const initialState: TCheckoutSliceState = {
  // 购物车 ID
  cartId: '',
  // 本单最大使用 N币 数量
  maxNCoinUsed: 0,
  // 用户 N币 信息
  userNCoinInfo: {
    // 用户 N币 余额
    totalBalance: 0,
    totalToExpire: 0,
  },
  // 购买自提商品 + 虚拟商品或只单独购买自提，返回 true
  isPickup: false,
  // 只单独购买虚拟商品，返回 true
  isVirtual: false,
  // 是否全是纯N币产品
  isNCoinPay: false,
  // 纯N币产品支付总价
  NCoinPayTotal: null,
  // 配送方式
  shippingMethods: [],
  // 支付方式
  paymentMethods: [],
  // 价格明细
  prices: {},
  // 产品数据
  products: [],
  // 保险产品 ID
  insuranceItemId: '',
  // 优惠券折扣信息
  couponsDiscount: [],
  // 下单数据
  placeOrderData: {
    addressId: null,
    shippingMethodCode: null,
    paymentMethodCode: null,
  },
  // 是否初始化完结算页流程
  initProcessComplete: false,
  // 发票信息
  invoice: {
    // 发票邮箱 (必填 长度不能超50)
    email: '',
    // 税号 (公司专票和公司普票 必填, 长度不能超过60)
    tax_id: null,
    // 发票抬头 (必填 长度不能超过100)
    title: '',
    // 发票类型 (必填 1: 个人, 2: 公司普票, 3: 公司专票)
    type: '',
  },
}

const checkoutSlice = createSlice({
  name: 'checkout',
  initialState,
  reducers: {
    /**
     * 设置购物车 ID
     */
    setCartId: (state, action) => {
      const { payload: cartId } = action

      if (cartId) {
        state.cartId = cartId
      }
    },
    /**
     * 保存 checkout 数据
     */
    setCheckoutData: (state, action: PayloadAction<TCheckoutCustomerCart>) => {
      const { payload } = action

      // save cart id
      if (!isUndefined(payload?.id) && !isNull(payload?.id)) {
        state.cartId = payload.id
      }

      // save is pickup
      if (!isUndefined(payload?.is_pickup) && !isNull(payload?.is_pickup)) {
        state.isPickup = payload.is_pickup
      }

      // save is virtual
      if (!isUndefined(payload?.is_virtual) && !isNull(payload?.is_virtual)) {
        state.isVirtual = payload.is_virtual
      }

      // save max ncoin used
      if (
        !isUndefined(payload?.max_ncoin_used) &&
        !isNull(payload?.max_ncoin_used) &&
        isNumber(payload.max_ncoin_used)
      ) {
        state.maxNCoinUsed = payload.max_ncoin_used
      }

      // save shipping methods
      if (
        !isUndefined(payload?.allShippingMethods) &&
        !isNull(payload?.allShippingMethods) &&
        payload.allShippingMethods?.length
      ) {
        // 过滤可用的 shipping methods
        const shippingMethods = payload.allShippingMethods.filter(
          (shippingMethod: TShippingMethods[number]) => shippingMethod?.available,
        )
        state.shippingMethods = shippingMethods
      }

      // save payment methods
      if (
        !isUndefined(payload?.available_payment_methods) &&
        !isNull(payload?.available_payment_methods) &&
        payload.available_payment_methods?.length
      ) {
        state.paymentMethods = payload.available_payment_methods
      }

      // save prices
      if (!isUndefined(payload?.prices) && !isNull(payload?.prices)) {
        if (!isNull(payload.prices?.discounts) && payload.prices.discounts?.length) {
          // 对 discounts 数据进行分组
          const discounts = payload.prices.discounts.reduce<{
            discounts: TCouponsDiscount
            couponsDiscount: TCouponsDiscount
          }>(
            (
              result: {
                discounts: TCouponsDiscount
                couponsDiscount: TCouponsDiscount
              },
              nextDiscount: TCouponsDiscount[number],
            ) => {
              if (nextDiscount?.coupon) {
                result.couponsDiscount.push(nextDiscount)
              } else {
                result.discounts.push(nextDiscount)
              }

              return result
            },
            {
              discounts: [],
              couponsDiscount: [],
            },
          )

          // 将分组后的 discounts 保存
          state.prices = { ...payload.prices, discounts: discounts.discounts }
          // 将分组后的 couponsDiscount 优惠券折扣信息保存
          state.couponsDiscount = discounts.couponsDiscount
        } else {
          const prices = { ...payload.prices }

          state.prices = prices
          state.couponsDiscount = []
        }
      }

      // save products
      if (
        !isUndefined(payload?.itemsV2?.items) &&
        !isNull(payload?.itemsV2?.items) &&
        payload.itemsV2.items?.length
      ) {
        state.products = formatProducts(payload.itemsV2.items)
      }

      // save insurance item id
      if (!isUndefined(payload?.insurance_item_id) && !isNull(payload?.insurance_item_id)) {
        state.insuranceItemId = payload.insurance_item_id
      }

      // save is NCoin pay
      if (!isUndefined(payload?.is_ncoin_pay) && !isNull(payload?.is_ncoin_pay)) {
        state.isNCoinPay = payload.is_ncoin_pay
      }

      // save NCoin pay total
      if (!isUndefined(payload?.ncoin_pay) && !isNull(payload?.ncoin_pay)) {
        state.NCoinPayTotal = payload.ncoin_pay
      }

      // save invoice info
      if (!isUndefined(payload?.invoice) && !isNull(payload?.invoice)) {
        state.invoice = payload.invoice
      }
    },
    /**
     * 设置下单时的 address id
     */
    setCheckoutPlaceOrderAddressId: (state, action) => {
      const { payload: addressId } = action

      state.placeOrderData.addressId = addressId
    },
    /**
     * 设置 shipping method，并且下单
     */
    setCheckoutPlaceOrderShippingMethodCode: (state, action) => {
      const { payload: shippingMethodCode } = action

      if (shippingMethodCode) {
        state.placeOrderData.shippingMethodCode = shippingMethodCode
      }
    },
    /**
     * 设置下单时的 payment method code
     */
    setCheckoutPlaceOrderPaymentMethodCode: (state, action) => {
      const { payload: paymentMethodCode } = action

      if (paymentMethodCode) {
        state.placeOrderData.paymentMethodCode = paymentMethodCode
      }
    },
    /**
     * 设置结算页是否初始化完成
     */
    setCheckoutInitProcessComplete: (state, action) => {
      const { payload: processComplete } = action

      if (!isUndefined(processComplete)) {
        state.initProcessComplete = processComplete
      }
    },
    /**
     * 设置用户 N币 信息
     */
    setCheckoutUserNCoinInfo: (state, action) => {
      const { payload: NCoinInfo } = action

      if (NCoinInfo) {
        state.userNCoinInfo = NCoinInfo
      }
    },
    /**
     * 重置 checkout 数据
     */
    resetCheckout: () => initialState,
    /**
     * 更新 shipping method price
     */
    updateCheckoutShippingMethodsAmount: (state, action) => {
      const { payload: shippingMethods } = action

      if (shippingMethods && Array.isArray(shippingMethods)) {
        state.shippingMethods.forEach((method: TShippingMethods[number]) => {
          const matchedItem = shippingMethods.find(
            (newItem: TShippingMethods[number]) => newItem?.method_code === method?.method_code,
          )
          if (matchedItem && method?.amount) {
            method.amount.value = matchedItem.amount.value
          }
        })
      }
    },
  },
})

const { actions } = checkoutSlice

export const {
  setCartId,
  setCheckoutData,
  setCheckoutPlaceOrderAddressId,
  setCheckoutPlaceOrderShippingMethodCode,
  setCheckoutPlaceOrderPaymentMethodCode,
  setCheckoutInitProcessComplete,
  setCheckoutUserNCoinInfo,
  resetCheckout,
  updateCheckoutShippingMethodsAmount,
} = actions

const selectSelf = (state: RootState) => state.checkout

/**
 * 获取购物车 ID
 */
export const cartIdSelector = createSelector(selectSelf, (state) => state.cartId)

/**
 * 获取用户填写的下单信息
 */
export const checkoutPlaceOrderDataSelector = createSelector(
  selectSelf,
  (state) => state.placeOrderData,
)

/**
 * 获取分组后的产品数据
 */
export const checkoutGroupedProductsSelector = createSelector(selectSelf, (state) => {
  return state.products.reduce<
    {
      code: string
      title: string
      products: TCheckoutCartProductItems
    }[]
  >(
    (
      result: {
        code: string
        title: string
        products: TCheckoutCartProductItems
      }[],
      product: TCheckoutCartProductItem | null,
    ) => {
      if (!product) return result

      const deliveryMethod = product?.product?.delivery_method
      const title = DELIVERY_METHOD_TEXT[deliveryMethod!] || DELIVERY_METHOD_TEXT.default

      const existingGroup = result.find((group: { code: string }) => group.code === deliveryMethod)

      if (existingGroup) {
        existingGroup.products.push(product)
      } else {
        result.push({
          code: deliveryMethod!,
          title,
          products: [product],
        })
      }

      return result
    },
    [],
  )
})

/**
 * 获取价格明细
 */
export const checkoutPricesSelector = createSelector(selectSelf, (state) => state.prices)

/**
 * 获取本单最大可用 N币 数量
 */
export const checkoutMaxNCoinUsedSelector = createSelector(
  selectSelf,
  (state) => state.maxNCoinUsed,
)

/**
 * 获取 payment methods
 */
export const checkoutPaymentMethodsSelector = createSelector(
  selectSelf,
  (state) => state.paymentMethods,
)

/**
 * 获取 shipping methods
 */
export const checkoutShippingMethodsSelector = createSelector(
  selectSelf,
  (state) => state.shippingMethods,
)

/**
 * 获取产品总件数
 */
export const checkoutProductsTotalSelector = createSelector(selectSelf, (state) =>
  state.products.reduce((total, product) => total + (product?.quantity || 0), 0),
)

/**
 * 获取下单地址 ID
 */
export const checkoutPlaceOrderAddressIdSelector = createSelector(
  checkoutPlaceOrderDataSelector,
  (placeOrderData) => placeOrderData.addressId,
)

/**
 * 获取选中的 shipping method
 */
export const checkoutSelectedShippingMethodSelector = createSelector(
  checkoutPlaceOrderDataSelector,
  checkoutShippingMethodsSelector,
  (placeOrderData, shippingMethods) => {
    return shippingMethods.find(
      (shippingMethod: TShippingMethods[number]) =>
        shippingMethod?.method_code === placeOrderData.shippingMethodCode,
    )
  },
)

/**
 * 获取选中的 payment method
 */
export const checkoutSelectedPaymentMethodSelector = createSelector(
  checkoutPlaceOrderDataSelector,
  checkoutPaymentMethodsSelector,
  (placeOrderData, paymentMethods) => {
    return paymentMethods.find(
      (paymentMethod: TPaymentMethods[number]) =>
        paymentMethod?.code === placeOrderData.paymentMethodCode,
    )
  },
)

/**
 * 获取当前结算页选中的地址
 */
export const checkoutSelectedShippingAddressSelector = createSelector(
  (state) => state,
  checkoutPlaceOrderDataSelector,
  (state, placeOrderData) => {
    if (placeOrderData.addressId) {
      return selectUserAddressById(state, placeOrderData.addressId)
    }

    return null
  },
)

/**
 * 获取购物车产品是否是自提状态
 */
export const checkoutIsPickupSelector = createSelector(selectSelf, (state) => {
  return state.isPickup
})

/**
 * 获取购物车产品是否是虚拟状态
 */
export const checkoutIsVirtualSelector = createSelector(selectSelf, (state) => {
  return state.isVirtual
})

/**
 * 获取是否需要设置 shipping address 和 billing address
 */
export const checkoutIsNeedSetShippingAddressAndBillingAddressSelector = createSelector(
  checkoutIsPickupSelector,
  checkoutIsVirtualSelector,
  (isPickup, isVirtual) => {
    return !(isPickup || isVirtual)
  },
)

/**
 * 获取是否需要设置 shipping methods
 */
export const checkoutIsNeedSetShippingMethodsSelector = createSelector(
  checkoutIsNeedSetShippingAddressAndBillingAddressSelector,
  checkoutIsVirtualSelector,
  (isNeedSetShippingAddressAndBillingAddress, isVirtual) => {
    if (isVirtual) return false
    return isNeedSetShippingAddressAndBillingAddress
  },
)

/**
 * 获取是否初始化完结算页流程
 */
export const checkoutIsInitProcessCompletedSelector = createSelector(
  selectSelf,
  (state) => state.initProcessComplete,
)

/**
 * 获取是否可以下单
 */
export const checkoutCanPlaceOrderSelector = createSelector(
  checkoutIsNeedSetShippingAddressAndBillingAddressSelector,
  checkoutIsNeedSetShippingMethodsSelector,
  checkoutSelectedShippingMethodSelector,
  checkoutSelectedPaymentMethodSelector,
  checkoutIsInitProcessCompletedSelector,
  (
    isNeedSetShippingAddressAndBillingAddress,
    isNeedSetShippingMethods,
    selectedShippingMethod,
    selectedPaymentMethod,
    isInitProcessCompleted,
  ) => {
    if (isNeedSetShippingAddressAndBillingAddress || isNeedSetShippingMethods) {
      return selectedShippingMethod && selectedPaymentMethod && isInitProcessCompleted
    }

    return isInitProcessCompleted
  },
)

/**
 * 获取用户 N币 信息
 */
export const checkoutUserNCoinInfoSelector = createSelector(
  selectSelf,
  (state) => state.userNCoinInfo,
)

/**
 * 获取保险产品 ID
 */
export const checkoutInsuranceItemId = createSelector(selectSelf, (state) => state.insuranceItemId)

/**
 * 获取是否有购买保险产品
 */
export const checkoutHasInsuranceItemSelector = createSelector(
  checkoutInsuranceItemId,
  (state) => !!state,
)

/**
 * 获取折扣信息
 */
export const checkoutDiscountsSelector = createSelector(checkoutPricesSelector, (prices) => {
  return prices?.discounts ?? []
})

/**
 * 获取优惠券折扣信息
 */
export const checkoutCouponsDiscountSelector = createSelector(
  selectSelf,
  (state) => state.couponsDiscount,
)

/**
 * 获取优惠券折扣总价
 */
export const checkoutCouponsDiscountAmountSelector = createSelector(
  checkoutCouponsDiscountSelector,
  (couponsDiscount) => {
    if (couponsDiscount.length) {
      return couponsDiscount.reduce((result: Big, nextDiscount: TCouponsDiscount[number]) => {
        return result.plus(new Big(Math.abs(nextDiscount?.distribute_amount ?? 0)))
      }, new Big(0))
    }

    return new Big(0)
  },
)

/**
 * 获取优惠券折扣N币总数
 */
export const checkoutCouponsDiscountNCoinAmountSelector = createSelector(
  checkoutCouponsDiscountSelector,
  (couponsDiscount) => {
    if (couponsDiscount.length) {
      return couponsDiscount.reduce((result, nextDiscount) => {
        return result.plus(new Big(Math.abs(nextDiscount?.distribute_ncoin ?? 0)))
      }, new Big(0))
    }
    return new Big(0)
  },
)

/**
 * 获取已应用的优惠券代码
 */
export const checkoutAppliedCouponCodeSelector = createSelector(
  checkoutCouponsDiscountSelector,
  (couponsDiscount) => {
    if (couponsDiscount.length) {
      // 返回优惠券代码数组
      return couponsDiscount
        .map((discount: TCouponsDiscount[number]) => discount?.coupon?.code ?? '')
        .filter(Boolean)
    }

    return []
  },
)

/**
 * 获取优惠券折扣 label
 */
export const checkoutCouponsDiscountLabelSelector = createSelector(
  checkoutCouponsDiscountSelector,
  (couponsDiscount) => {
    if (couponsDiscount.length) {
      return couponsDiscount.reduce<string>(
        (result: string, nextDiscount: TCouponsDiscount[number]) => {
          if (result.length) {
            result = `${result} + ${nextDiscount?.label}`
          }

          return nextDiscount?.label ?? ''
        },
        '',
      )
    }

    return ''
  },
)

/**
 * 获取是否包含优惠券折扣信息
 */
export const checkoutHasCouponsDiscountSelector = createSelector(
  checkoutCouponsDiscountSelector,
  (couponsDiscount) => {
    return !!couponsDiscount.length
  },
)

/**
 * 获取N币抵扣折扣
 */
export const checkoutNCoinDiscountSelector = createSelector(checkoutPricesSelector, (prices) => {
  return prices?.ncoin_deduction
})

/**
 * 是否有N币抵扣折扣
 */
export const checkoutHasNCoinDiscountSelector = createSelector(
  checkoutNCoinDiscountSelector,
  (NCoinDiscount) => {
    return NCoinDiscount?.amount?.value !== 0
  },
)

/**
 * 获取优惠总金额 checkoutCouponsDiscountAmountSelector + checkoutCouponsDiscountLabelSelector 后续需要展示共减需要加上checkoutCouponsDiscountLabelSelector的值
 */
export const checkoutTotalDiscountAmountSelector = createSelector(
  checkoutDiscountsSelector,
  checkoutCouponsDiscountAmountSelector,
  checkoutNCoinDiscountSelector,
  (discounts, couponsDiscountAmount, NCoinDiscount) => {
    let totalAmount = new Big(0)

    // 累加 discounts
    totalAmount = discounts?.reduce((result: Big, nextDiscount: TCouponsDiscount[number]) => {
      result = result.plus(new Big(Math.abs(nextDiscount?.amount?.value ?? 0)))
      return result
    }, totalAmount)

    // 累加N币抵扣
    if (!isUndefined(NCoinDiscount?.amount.value)) {
      totalAmount = totalAmount.plus(new Big(Math.abs(NCoinDiscount?.amount?.value ?? 0)))
    }

    // 累加优惠券
    totalAmount = totalAmount.plus(couponsDiscountAmount)

    return totalAmount
  },
)

/**
 * 获取购物车产品中是否有自提产品
 */
export const checkoutHasPickupProductsSelector = createSelector(
  checkoutGroupedProductsSelector,
  (groupedProducts) => {
    if (!groupedProducts) return false

    return groupedProducts.some(
      (group: { code: string }) => group.code === DELIVERY_METHOD_TYPE.store_pickup,
    )
  },
)

/**
 * 获取是否全是纯N币产品
 */
export const checkoutIsNCoinPaySelector = createSelector(selectSelf, (state) => state.isNCoinPay)

/**
 * 获取纯N币产品支付总价
 */
export const checkoutNCoinPayTotalSelector = createSelector(
  selectSelf,
  (state) => state.NCoinPayTotal,
)

/**
 * 根据本地产品数据，判断产品是否全部是纯N币支付产品
 */
export const checkoutIsAllPureProductsByLocalSelector = createSelector(selectSelf, (state) => {
  return state.products.every((product: TCheckoutCartProductItem | null) => {
    if (!product) return false
    return product?.product?.paymeng_method === ONLY_N_COIN
  })
})

/**
 * 获取发票信息
 */
export const checkoutInvoiceSelector = createSelector(selectSelf, (state) => state.invoice)

export default checkoutSlice
