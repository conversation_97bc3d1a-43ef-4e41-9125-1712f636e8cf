'use client'

import clsx from 'clsx'

import { SoldOutTag } from '@/components/icons'
import type { ProductStatus } from '@/types/product'
interface OptionItem {
  value_index: number
  label: string
}

interface SizeSelectorProps {
  optionItems: OptionItem[]
  id: string
  productStatus: ProductStatus
  onSelectionChange: (item: OptionItem, id: string) => void
}

/**
 * 尺码选择器组件
 * @param optionItems - 可选尺码列表
 * @param id - 选项ID
 * @param productStatus - 商品状态信息
 * @param onSelectionChange - 选择变更回调
 */
const SizeSelector: React.FC<SizeSelectorProps> = ({
  optionItems,
  id,
  productStatus,
  onSelectionChange,
}) => {
  const { optionSelections, outOfStockVariants, isEverythingOutOfStock } = productStatus
  const optionId = optionSelections?.get(id)

  return (
    <div className="flex flex-wrap gap-base-12">
      {optionItems.map((item) => {
        let isOptionOutOfStock = false
        if (outOfStockVariants?.length) {
          const flatOutOfStockArray = outOfStockVariants?.flat()
          isOptionOutOfStock = !!flatOutOfStockArray?.includes(item.value_index)
        }

        return (
          <button
            key={item.value_index}
            disabled={isOptionOutOfStock}
            className={clsx(
              'relative rounded-base border px-base-16 py-base text-base transition-colors',
              optionId === item.value_index
                ? 'border-primary bg-[#EC1D260D] text-primary'
                : 'bg-gray-base',
              isOptionOutOfStock && ['cursor-not-allowed'],
            )}
            onClick={() => {
              if (
                !(isEverythingOutOfStock || isOptionOutOfStock) &&
                optionId !== item.value_index
              ) {
                onSelectionChange(item, id)
              }
            }}>
            {item.label}
            {isOptionOutOfStock && (
              <div className="absolute -top-2 right-0 transform">
                <SoldOutTag />
              </div>
            )}
          </button>
        )
      })}
    </div>
  )
}

export default SizeSelector
