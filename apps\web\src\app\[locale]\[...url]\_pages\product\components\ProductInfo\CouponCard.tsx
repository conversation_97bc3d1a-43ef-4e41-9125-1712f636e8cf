import { useCallback, useEffect, useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  hasDecimal,
  mergeStyles,
  Price,
  ProductCoupons,
  resolveCatchMessage,
  TCatchMessage,
  useReceiveCouponMutation,
  useToastContext,
} from '@ninebot/core'

import { Sign } from '@/components/business/checkout/icons'

type CouponCardProps = {
  coupon: ProductCoupons
  componentId: number
  isLoading?: boolean
  onCouponReceived?: (ruleId: number) => void
}

/**
 * PC端优惠券卡片组件
 * 用于在领券弹窗中展示优惠券详情和领取功能
 */
const CouponCard = ({
  coupon,
  componentId,
  isLoading = false,
  onCouponReceived,
}: CouponCardProps) => {
  const toast = useToastContext()
  const getI18nString = useTranslations('Common')
  const [disabled, setDisabled] = useState(true)
  const [receiveCoupon] = useReceiveCouponMutation()

  // 处理优惠券领取
  const handleReceive = useCallback(async () => {
    if (isLoading || disabled) {
      return
    }

    setDisabled(true)
    try {
      await receiveCoupon({
        ruleId: String(coupon?.rule_id),
        componentId: String(componentId),
        couponType: '1', // PDP页面类型
      })
        .unwrap()
        .then((res) => {
          const couponRes = res?.generate_receive_coupon
          if (couponRes) {
            if (onCouponReceived) {
              onCouponReceived(Number(coupon?.rule_id))
            }
            setDisabled(!!couponRes?.receicved)
            toast.show({
              content: couponRes?.message as string,
              icon: 'success',
            })
          }
        })
    } catch (error) {
      setDisabled(false)
      toast.show({
        content:
          (resolveCatchMessage(error as TCatchMessage) as string) ||
          getI18nString('fetch_data_error'),
        icon: 'fail',
      })
    }
  }, [
    isLoading,
    disabled,
    receiveCoupon,
    coupon,
    componentId,
    toast,
    getI18nString,
    onCouponReceived,
  ])

  /**
   * 渲染优惠信息
   */
  const renderDiscount = useCallback(() => {
    if (Number(coupon?.discount) === 0) {
      return (
        <span className="font-miSansDemiBold450 text-[20px] leading-none text-[#DA291C]">
          {coupon?.rule_label}
        </span>
      )
    }

    if (['cart_fixed', 'by_fixed'].includes(String(coupon?.discount_type))) {
      return (
        <Price
          bold
          color="primary"
          currencyStyle="text-[18px] leading-[1.2] text-[#DA291C]"
          textStyle="font-miSansDemiBold450 text-[40px] leading-none text-[#DA291C]"
          showFraction={false}
          price={{ value: Number(coupon?.discount) }}
        />
      )
    }

    if (['by_percent'].includes(String(coupon?.discount_type))) {
      const amount = Number(coupon?.discount)
      return (
        <div className="flex items-baseline">
          <span className="font-miSansDemiBold450 text-[28px] leading-[1.2] text-[#DA291C]">
            {hasDecimal(amount) ? amount.toFixed(1) : amount.toString()}
          </span>
          <span className="text-[18px] leading-[1.2] text-[#DA291C]">
            {getI18nString('coupon_code_percent')}
          </span>
        </div>
      )
    }

    return <span className="text-[40px] leading-none text-[#DA291C]">{coupon?.discount}</span>
  }, [coupon, getI18nString])

  useEffect(() => {
    setDisabled(isLoading || !!coupon?.status)
  }, [isLoading, coupon?.status])

  return (
    <div className="relative flex h-[110px] w-[326px] items-center justify-between overflow-hidden rounded-[12px] bg-[#FEE5E560] p-[4px]">
      {/* 左侧优惠金额区域 */}
      <div className="flex h-full flex-1 flex-col items-center justify-center gap-[4px] p-base text-primary">
        {/* 优惠券金额 */}
        <div className="flex items-baseline">{renderDiscount()}</div>
        {/* 优惠券类型 */}
        {coupon.short_description && (
          <div
            className={mergeStyles(
              'text-center font-miSansRegular330 leading-[1.2] text-primary',
              !coupon?.is_ncoin ? 'text-[14px]' : 'text-[12px]',
            )}>
            {coupon.short_description}
          </div>
        )}
      </div>

      {/* 右侧优惠券信息区域 */}
      <div className="relative flex h-[102px] w-[217px] flex-col justify-center rounded-[8px] bg-white p-base-12">
        {/* 上侧半圆遮盖 */}
        <div className="absolute -top-[6px] right-[22.7%] h-[12px] w-[12px] rounded-full bg-[#FEE5E560] 2xl:right-[33.7%]"></div>

        {/* 下侧半圆遮盖 */}
        <div className="absolute -bottom-[6px] right-[22.7%] h-[12px] w-[12px] rounded-full bg-[#FEE5E560] 2xl:right-[33.7%]"></div>

        {/* 优惠券装饰图案 - 右下角 */}
        <div className="absolute bottom-0 right-[12px] z-10">
          <Sign fill="#FEE5E5CC" />
        </div>

        <div className="flex items-center justify-between">
          {/* 优惠券信息 */}
          <div className="flex flex-col gap-[4px]" style={{ width: '109px' }}>
            {/* 优惠券标题 */}
            <div className="truncate font-miSansMedium380 text-[16px] leading-[140%] text-[#000000]">
              {coupon?.title}
            </div>

            {/* 有效期 */}
            {coupon?.description && (
              <div className="font-miSansRegular330 text-[12px] leading-[133%] text-[#86868B]">
                {coupon?.description}
              </div>
            )}
          </div>

          {/* 领取按钮 */}
          <button
            disabled={disabled}
            onClick={handleReceive}
            className={mergeStyles(
              'z-10 flex h-[24px] items-center justify-center rounded-[101px] px-[12px] py-[4px] font-miSansRegular330 text-[12px] leading-none',
              disabled
                ? 'border-0 bg-[#E1E1E4] text-[#0F0F0F] hover:bg-[#E1E1E4] hover:text-[#0F0F0F]'
                : 'border border-[#DA291C] bg-white text-[#DA291C] hover:bg-[#DA291C] hover:text-white',
            )}>
            {disabled ? '已领取' : coupon?.status_label}
          </button>
        </div>
      </div>
    </div>
  )
}

export default CouponCard
