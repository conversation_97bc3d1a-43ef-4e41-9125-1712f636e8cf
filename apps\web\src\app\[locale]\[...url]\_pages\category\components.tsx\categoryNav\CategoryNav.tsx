'use client'

import { useEffect, useRef, useState } from 'react'
import { useTranslations } from 'next-intl'
import { mergeStyles } from '@ninebot/core'

import { IconArrow } from '@/components'

import { TCategoryNav } from '../categoryList/CategoryList'

interface CategoryNavProps {
  currentCategory: TCategoryNav
  categories?: TCategoryNav[]
  selectedCategoryId: string // 新增：当前选中的分类ID
  onCategorySelect: (categoryId: string) => void // 新增：分类选择回调
}

const CategoryNav = ({
  currentCategory,
  categories,
  selectedCategoryId,
  onCategorySelect,
}: CategoryNavProps) => {
  const getI18nString = useTranslations('Web')
  const navRef = useRef<HTMLDivElement>(null)
  const [isSticky, setIsSticky] = useState(false)
  const [expandedCategories, setExpandedCategories] = useState<string[]>(() => {
    // 初始化时展开包含当前选中分类的父分类
    if (categories) {
      return categories
        .filter((category) => {
          // 如果当前选中的分类是这个分类的子分类，则展开
          return (
            category.children?.some((child) => child.id === selectedCategoryId) &&
            category.children?.length
          )
        })
        .map((category) => category.id.toString())
    }
    return []
  })

  // 监听selectedCategoryId变化，自动展开相应的父分类
  useEffect(() => {
    if (categories) {
      const parentCategories = categories
        .filter((category) => {
          // 如果当前选中的分类是这个分类的子分类，则展开
          return category.children?.some((child) => child.id === selectedCategoryId)
        })
        .map((category) => category.id.toString())

      setExpandedCategories((prev) => {
        // 合并已展开的分类和需要新展开的分类
        const newExpanded = [...new Set([...prev, ...parentCategories])]
        return newExpanded
      })
    }
  }, [selectedCategoryId, categories])

  // 监听滚动，实现吸顶效果
  useEffect(() => {
    if (!navRef.current) return

    // 创建一个sentinel元素来监听
    const sentinel = document.createElement('div')
    sentinel.style.height = '1px'
    sentinel.style.position = 'absolute'
    sentinel.style.top = '56px' // Header高度
    sentinel.style.left = '0'
    sentinel.style.width = '100%'
    sentinel.style.pointerEvents = 'none'

    // 将sentinel插入到导航元素之前
    navRef.current.parentNode?.insertBefore(sentinel, navRef.current)

    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0]
        // 当sentinel离开视口时，启用吸顶
        setIsSticky(!entry.isIntersecting)
      },
      {
        threshold: 0,
      },
    )

    observer.observe(sentinel)

    return () => {
      observer.disconnect()
      sentinel.remove()
    }
  }, [])

  // 切换展开/收起状态
  const toggleExpand = (categoryId: string) => {
    setExpandedCategories((prev) =>
      prev.includes(categoryId) ? prev.filter((id) => id !== categoryId) : [...prev, categoryId],
    )
  }

  // 检查分类是否为当前选中的分类
  const isCurrentCategory = (categoryId: string) => selectedCategoryId === categoryId

  // 如果没有子分类,则不显示导航
  if (!categories?.length) {
    return null
  }

  return (
    <div
      ref={navRef}
      className={mergeStyles(
        'responsive-category-nav border-none bg-white transition-all duration-300',
        isSticky ? 'sticky top-[140px] z-10 max-h-[calc(100vh-140px)] overflow-y-auto' : '',
      )}>
      <ul>
        {/* 当前分类作为"全部" */}
        <li>
          <button
            onClick={() => onCategorySelect(currentCategory.id)}
            className={mergeStyles(
              'block h-[48px] w-full px-[26px] py-[12px] text-left hover:rounded-full hover:bg-gray-base',
              'font-miSansMedium380 text-[16px] leading-[140%]',
              isCurrentCategory(currentCategory.id) ? 'text-primary' : 'text-[#000000]',
            )}>
            <span>{getI18nString('all')}</span>
          </button>
        </li>

        {/* 子分类列表 */}
        {categories.map((category) =>
          category.id ? (
            <li key={category.id} className="mt-[4px]">
              {category.children?.length ? (
                // 有三级分类的二级分类
                <div>
                  <button
                    onClick={() => toggleExpand(category.id.toString())}
                    className={mergeStyles(
                      'flex h-[48px] w-full items-center justify-between px-[26px] py-[12px] hover:rounded-full hover:bg-gray-base hover:text-primary',
                      'font-miSansMedium380 text-[16px] leading-[140%]',
                      isCurrentCategory(category.id) ? 'text-primary' : 'text-[#000000]',
                    )}>
                    <span>{category.name}</span>
                    <IconArrow
                      size={16}
                      rotate={expandedCategories.includes(category.id.toString()) ? 180 : 0}
                    />
                  </button>

                  {/* 展开的三级分类 */}
                  {expandedCategories.includes(category.id.toString()) && (
                    <ul>
                      <li className="mt-[4px]">
                        <button
                          onClick={() => onCategorySelect(category.id)}
                          className={mergeStyles(
                            'block h-[48px] w-full px-[26px] py-[12px] text-left hover:rounded-full hover:bg-gray-base hover:text-primary',
                            'font-miSansMedium380 text-[16px] leading-[140%]',
                            isCurrentCategory(category.id) ? 'text-primary' : 'text-[#000000]',
                          )}>
                          <span>{getI18nString('all')}</span>
                        </button>
                      </li>
                      {category.children.map(
                        (subCategory) =>
                          subCategory.id && (
                            <li key={subCategory.id} className="mt-[4px]">
                              <button
                                onClick={() => onCategorySelect(subCategory.id)}
                                className={mergeStyles(
                                  'block h-[48px] w-full px-[26px] py-[12px] text-left hover:rounded-full hover:bg-gray-base hover:text-primary',
                                  'font-miSansMedium380 text-[16px] leading-[140%]',
                                  isCurrentCategory(subCategory.id)
                                    ? 'text-primary'
                                    : 'text-[#000000]',
                                )}>
                                <span>{subCategory.name}</span>
                              </button>
                            </li>
                          ),
                      )}
                    </ul>
                  )}
                </div>
              ) : // 无三级分类的二级分类
              category.id ? (
                <button
                  onClick={() => onCategorySelect(category.id)}
                  className={mergeStyles(
                    'mt-[4px] block h-[48px] w-full px-[26px] py-[12px] text-left hover:rounded-full hover:bg-gray-base hover:text-primary',
                    'font-miSansMedium380 text-[16px] leading-[140%]',
                    isCurrentCategory(category.id) ? 'text-primary' : 'text-[#000000]',
                  )}>
                  <span>{category.name}</span>
                </button>
              ) : null}
            </li>
          ) : null,
        )}
      </ul>
    </div>
  )
}

export default CategoryNav
