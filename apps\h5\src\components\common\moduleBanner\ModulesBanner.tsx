'use client'
import React from 'react'
import { generateEventParams, RelationItem, TRACK_EVENT, useVolcAnalytics } from '@ninebot/core'
import { useNavigate } from '@ninebot/core/src/businessHooks'
import { Image } from 'antd-mobile'

import { Link } from '@/i18n/navigation'

// import { Arrow } from '@/components'

const FONT_FAMILY = {
  bold: 'font-miSansSemibold520',
  medium: 'font-miSansDemiBold450',
  regular: 'font-miSansRegular330',
}

/**
 * 模块 Banner 组件
 * @param {Object} props - 组件参数
 * @param {string} props.title - 活动的主标题
 * @param {string} props.subtitle - 活动的副标题
 * @param {string} props.image_url - 活动的图片 URL
 * @param {string} props.description - 活动的描述信息
 * @param {string} props.button_text - 按钮的显示文字，默认为空字符串
 * @param {boolean} props.button_arrow - 是否显示按钮箭头，默认值为 `false`
 * @param {Object} props.button_url - 按钮的链接信息
 * @param {Function} props.openPage - 用于打开页面的函数
 * @param {Object} props.imageRatio - 图片比例对象，默认值为 `{ width: 1, height: 1 }`
 * @param {number} [props.imageRatio.width=1] - 图片的宽度比例，默认为 1
 * @param {number} [props.imageRatio.height=1] - 图片的高度比例，默认为 1
 * @param {boolean} [props.fullscreen=false] - 是否使用全屏模式，默认值为 `false`
 * @param {boolean} [props.isHome=false] - 是否为首页，默认值为 `false`
 * @param {Object} [props.containerStyle={}] - 自定义容器样式，默认值为空对象
 *
 * @returns {JSX.Element} banner组件
 */

type ModuleBannerProps = {
  banner: RelationItem
  containerStyle?: string
  imageRatio: {
    width: number
    height: number
  }
  isHome?: boolean
}

type ButtonUrl = {
  type?: string | undefined
  url?: string | undefined
  value?: string | undefined
}

const ModuleBanner = ({
  banner,
  containerStyle = '',
  imageRatio,
  isHome = false,
}: ModuleBannerProps) => {
  const { openPage } = useNavigate()
  const { reportEvent } = useVolcAnalytics()

  if (!banner) return null

  const {
    title,
    subtitle,
    image_url: image,
    description,
    button_text: buttonText,
    // button_arrow: showArrow,
    button_url,
    fullscreen = false,
  } = banner
  const { width: widthRatio, height: heightRatio } = imageRatio

  const bannerStyle = fullscreen
    ? {
        height: '100vh',
      }
    : {
        aspectRatio: Number(widthRatio) / Number(heightRatio),
        height: '100%',
      }

  const handlePress = (item: ButtonUrl) => {
    if (!item?.value && !item?.url) {
      return
    }

    if (isHome) {
      reportEvent(
        TRACK_EVENT.shop_homepage_banner_picture_click,
        generateEventParams({ ...item, label: title }),
      )
    }

    openPage({ ...item })
  }

  if (banner?.button_url?.type === 'seckill') {
    return null
  }

  return (
    <Link
      className={`w-100% relative mb-[16px] block ${containerStyle}`}
      style={bannerStyle}
      href={button_url?.url || 'javascript:void(0)'}
      onClick={() => handlePress(button_url as ButtonUrl)}>
      <Image
        className="h-full w-full rounded-[12px]"
        src={image || ''}
        fit="cover"
        alt={title || ''}
      />
      <div className="absolute left-[10px] top-[16px] flex flex-col items-start">
        {subtitle ? (
          <button className="flex items-center justify-center rounded-full bg-[#DA291C] px-[16px] py-[8px]">
            <span
              className={`${FONT_FAMILY.medium} line-clamp-1 text-[12px] leading-[1.2] text-[#FFFFFF]`}>
              {subtitle}
            </span>
          </button>
        ) : null}
        {title ? (
          <div
            className={`mt-base-12 line-clamp-1 font-miSansDemiBold450 text-[18px] leading-[22px] text-[#FFFFFF]`}>
            {title}
          </div>
        ) : null}
        {description ? (
          <div
            className={`mt-[6px] line-clamp-1 font-miSansDemiBold450 text-[18px] leading-[22px] text-[#FFFFFF]`}>
            {description}
          </div>
        ) : null}
        {buttonText ? (
          <div className="mt-base-16 font-miSansDemiBold450 text-[16px] leading-[1.2] text-[#FFFFFF]">
            {buttonText}
          </div>
        ) : null}
      </div>
    </Link>
  )
}

export default ModuleBanner
