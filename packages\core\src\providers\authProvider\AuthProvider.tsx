'use client'

import { useEffect, useRef } from 'react'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'

import { useLoadingContext, useToastContext } from '../../components'
import { LOGIN_REFERER_URL, PASSPORT_LOGIN_CODE } from '../../constants'
import { usePassportLoginMutation } from '../../services'
import { setUserAuthLoading, setUserAuthToken } from '../../store'
import { useAppDispatch } from '../../store/hooks'
import { TBaseComponentProps } from '../../typings'
import { appLocalStorage, resolveCatchMessage, TCatchMessage } from '../../utils'

type TAuthProviderProps = TBaseComponentProps

/**
 * 处理授权相关内容组件
 */
const AuthProvider = (props: TAuthProviderProps) => {
  const dispatch = useAppDispatch()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const router = useRouter()

  const loading = useLoadingContext()
  const toast = useToastContext()

  const [passportLoginMutation] = usePassportLoginMutation()
  // 获取第三方返回的授权 code
  const authCode = searchParams.get(PASSPORT_LOGIN_CODE)
  // 为了控制开发模式下，重复渲染导致多次请求，授权只能请求一次
  const isAuthFetch = useRef(false)

  /**
   *  如果 URL 有 code ，则进行商城授权登录
   */
  useEffect(() => {
    if (authCode && !isAuthFetch.current) {
      isAuthFetch.current = true

      // 处理成登录之前的 URL
      const searchParamsCloned = new URLSearchParams(searchParams.toString())
      searchParamsCloned.delete(PASSPORT_LOGIN_CODE)
      const urlRemovedAuthCode =
        searchParamsCloned.size > 0
          ? `${pathname}?${searchParamsCloned.toString()}${window.location.hash}`
          : pathname + window.location.hash

      loading.show()
      const response = passportLoginMutation({
        code: authCode,
        callbackUrl: window.location.origin + urlRemovedAuthCode,
      })

      response
        .unwrap()
        .then((res) => {
          if (res.passport_web_login) {
            const resToken = res.passport_web_login
            dispatch(
              setUserAuthToken({
                refreshToken: resToken?.refresh_token?.token,
                refreshTokenExpired: resToken?.refresh_token?.expired,
                token: resToken?.token?.token,
                tokenExpired: resToken?.token?.expired,
              }),
            )
            // 登录成功，授权完成
            dispatch(setUserAuthLoading(false))
            const loginRefererUrl = appLocalStorage.getItemSync<string | null>(LOGIN_REFERER_URL)
            if (loginRefererUrl) {
              router.replace(loginRefererUrl)
              return
            }

            router.replace('/')
          }
        })
        .catch((error) => {
          // 登录失败，也要将授权调整为完成
          dispatch(setUserAuthLoading(false))
          toast.show({
            icon: 'fail',
            content: resolveCatchMessage(error as TCatchMessage) as string,
          })

          router.replace('/')
        })
        .finally(() => {
          // 登录完成后，清除 URL 上的 code
          // router.replace(urlRemovedAuthCode)
          appLocalStorage.removeItemSync(LOGIN_REFERER_URL)
          loading.hide()
        })
    } else {
      // 没有授权默认调整为授权完成
      dispatch(setUserAuthLoading(false))
    }
  }, [authCode, dispatch, passportLoginMutation, router, pathname, searchParams, loading, toast])

  return props.children
}

export default AuthProvider
