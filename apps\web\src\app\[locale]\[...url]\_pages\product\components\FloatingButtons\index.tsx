'use client'

import { useCallback, useEffect, useState } from 'react'

import CustomerService from './CustomerService'

const FloatingButtons = () => {
  const [showScrollTop, setShowScrollTop] = useState(false)

  // 处理滚动事件
  useEffect(() => {
    const handleScroll = () => {
      // 当页面滚动超过300px时显示按钮
      setShowScrollTop(window.scrollY > 800)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const handleScrollToTop = useCallback(() => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    })
  }, [])

  return (
    <div className="fixed bottom-[108px] right-[32px] flex flex-col gap-base-24">
      {showScrollTop && (
        <button
          onClick={handleScrollToTop}
          className="flex h-[56px] w-[56px] items-center justify-center rounded-full bg-[#000] transition-opacity hover:opacity-80"
          aria-label="返回顶部">
          <svg
            width="28"
            height="28"
            viewBox="0 0 28 28"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
              d="M4.82172 15.8251L6.30664 17.3149L13.7313 9.86554L13.7313 8.37565L12.2463 8.37565L4.82172 15.8251Z"
              fill="white"
            />
            <path
              d="M23.5523 15.8251L22.0674 17.3149L14.6428 9.86554L14.6428 8.37566L16.1277 8.37565L23.5523 15.8251Z"
              fill="white"
            />
            <path
              d="M15.3207 24.502H13.2207V12.0421L14.2707 10.9886L15.3207 12.0421L15.3207 24.502Z"
              fill="white"
            />
            <path
              d="M24.5 3.50041L24.5 5.60742L15.05 5.60742L12.8333 3.50195L15.05 3.50041L24.5 3.50041Z"
              fill="white"
            />
            <path
              d="M3.5 3.50041L3.5 5.60742L12.95 5.60742L15.75 5.60742L12.95 3.50041L3.5 3.50041Z"
              fill="white"
            />
          </svg>
        </button>
      )}

      <CustomerService />
    </div>
  )
}

export default FloatingButtons
