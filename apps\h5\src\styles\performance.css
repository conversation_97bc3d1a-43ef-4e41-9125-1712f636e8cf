/* 性能优化样式 */

/* 优化滚动性能 */
.product-page {
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

/* 优化图片加载性能 */
.product-image {
  will-change: opacity;
  transition: opacity 0.3s ease;
}

/* 优化轮播图性能 */
.product-swiper {
  will-change: transform;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

/* 减少重绘和重排 */
.product-content {
  contain: layout style paint;
}

/* 优化滚动容器 */
.scroll-container {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}

/* 预加载关键CSS */
.critical-css {
  display: none;
}

/* 优化动画性能 */
.optimize-animation {
  will-change: transform, opacity;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

/* 减少合成层 */
.reduce-composite {
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* 优化触摸响应 */
.touch-optimized {
  touch-action: manipulation;
}

/* 减少布局抖动 */
.layout-stable {
  position: relative;
  z-index: 1;
}

/* 优化字体渲染 */
.font-smooth {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* 减少内存使用 */
.memory-efficient {
  contain: strict;
}

/* 优化滚动条 */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
