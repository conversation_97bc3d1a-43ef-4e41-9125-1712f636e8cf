/* 响应式产品图片轮播容器 */
.responsive-product-gallery {
  width: 620px;
  height: 620px;
}

/* 小于1024px时保持486px尺寸 */
@media (max-width: 1024px) {
  .responsive-product-gallery {
    width: 486px;
    height: 486px;
  }
}

@media (min-width: 1024px) and (max-width: 1439px) {
  .responsive-product-gallery {
    width: clamp(486px, 486px + ((100vw - 1024px) / 416) * 216, 620px);
    height: clamp(486px, 486px + ((100vw - 1024px) / 416) * 216, 620px);
  }
}

/* 响应式放大镜区域 - 与右侧信息区域宽度保持一致，宽高比1:1 */
.responsive-magnifier-area {
  width: 500px;
  height: 500px;
}

/* 小于1024px时保持434px尺寸 */
@media (max-width: 1024px) {
  .responsive-magnifier-area {
    width: 434px;
    height: 434px;
  }
}

@media (min-width: 1024px) and (max-width: 1439px) {
  .responsive-magnifier-area {
    width: clamp(434px, 434px + ((100vw - 1024px) / 416) * 66, 500px);
    height: clamp(434px, 434px + ((100vw - 1024px) / 416) * 66, 500px);
  }
}

/* 响应式放大镜背景尺寸 - 保持2倍放大效果 */
.responsive-magnifier-bg {
  background-size: 1000px 1000px;
}

/* 小于1024px时保持868px尺寸 */
@media (max-width: 1024px) {
  .responsive-magnifier-bg {
    background-size: 868px 868px;
  }
}

@media (min-width: 1024px) and (max-width: 1439px) {
  .responsive-magnifier-bg {
    background-size: clamp(868px, 868px + ((100vw - 1024px) / 416) * 132, 1000px)
      clamp(868px, 868px + ((100vw - 1024px) / 416) * 132, 1000px);
  }
}

/* 响应式缩略图容器 */
.responsive-thumbnail-container {
  max-width: 612px;
}

/* 小于1024px时保持440px最大宽度 */
@media (max-width: 1024px) {
  .responsive-thumbnail-container {
    max-width: 440px;
  }
}

@media (min-width: 1024px) and (max-width: 1439px) {
  .responsive-thumbnail-container {
    max-width: clamp(440px, 440px + ((100vw - 1024px) / 416) * 216, 656px);
  }
}

/* 响应式缩略图尺寸 */
.responsive-thumbnail-item {
  width: 86px;
  height: 86px;
}

/* 小于1024px时保持60px尺寸 */
@media (max-width: 1024px) {
  .responsive-thumbnail-item {
    width: 60px;
    height: 60px;
  }
}

@media (min-width: 1024px) and (max-width: 1439px) {
  .responsive-thumbnail-item {
    width: clamp(60px, 60px + ((100vw - 1024px) / 416) * 26, 86px);
    height: clamp(60px, 60px + ((100vw - 1024px) / 416) * 26, 86px);
  }
}

/* 响应式缩略图间距 - 始终保持16px */
.responsive-thumbnail-spacing {
  padding-left: 8px;
  padding-right: 8px;
  font-size: 0;
}

/* 响应式轮播箭头位置 */
.responsive-carousel-arrow-left {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 16px;
}

.responsive-carousel-arrow-right {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 16px;
}

@media (min-width: 1024px) and (max-width: 1920px) {
  .responsive-carousel-arrow-left {
    left: clamp(16px, 16px + ((100vw - 1024px) / 896) * 52, 68px);
  }

  .responsive-carousel-arrow-right {
    right: clamp(16px, 16px + ((100vw - 1024px) / 896) * 52, 68px);
  }
}

@media (min-width: 1920px) {
  .responsive-carousel-arrow-left {
    left: 68px;
  }

  .responsive-carousel-arrow-right {
    right: 68px;
  }
}
