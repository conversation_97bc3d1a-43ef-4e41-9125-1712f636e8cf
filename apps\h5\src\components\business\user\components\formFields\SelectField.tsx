'use client'

import React from 'react'
import { Input } from 'antd-mobile'
import type { FC } from 'react'

import { Arrow } from '@/components'

interface SelectFieldProps {
  value?: string
  onChange?: (value: string) => void
  placeholder?: string
  styles?:
    | (React.CSSProperties &
        Partial<Record<'--color' | '--font-size' | '--placeholder-color' | '--text-align', string>>)
    | undefined
}

const SelectField: FC<SelectFieldProps> = ({ value, onChange, placeholder, styles }) => {
  const triggerValue = (changedValue: string) => {
    onChange?.(changedValue)
  }

  const onRealValueChange = (value: string) => {
    triggerValue(value)
  }

  return (
    <>
      <div className="flex w-full flex-row items-center justify-between gap-[24px]">
        <Input
          placeholder={placeholder}
          value={value}
          onChange={onRealValueChange}
          style={{ '--font-size': '16px', ...styles }}
          readOnly
        />
        <div className="flex flex-row items-center gap-[12px]">
          <Arrow size={20} rotate={90} color="#86868B" />
          <span className="text-[#DA291C]">*</span>
        </div>
      </div>
    </>
  )
}

export default SelectField
