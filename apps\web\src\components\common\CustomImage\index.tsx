'use client'

import React, { useCallback, useEffect, useState } from 'react'
import Image, { ImageProps } from 'next/image'
/**
 * 默认占位符图片路径（使用本地图片资源）
 */
import DEFAULT_PLACEHOLDER from '@public/images/default.png'
import clsx from 'clsx'

export type ImageDisplayMode = 'fill' | 'fixed' | 'responsive'

export interface CustomImageProps extends Omit<ImageProps, 'fill'> {
  /**
   * 图片显示模式
   * - fill: 填充父容器 (需要父元素有position: relative和固定高度)
   * - fixed: 固定尺寸 (需要提供width和height)
   * - responsive: 响应式 (保持宽高比，宽度100%)
   */
  displayMode?: ImageDisplayMode
  /**
   * 宽高比例 (仅在responsive模式下生效)
   * 格式: 16/9, 4/3, 1/1 等
   */
  aspectRatio?: string
  /**
   * 容器类名
   */
  containerClassName?: string
  /**
   * 显示加载占位符
   */
  showPlaceholder?: boolean
  /**
   * 占位符背景色
   */
  placeholderColor?: string
  /**
   * 对象适应方式
   */
  objectFit?: 'cover' | 'contain' | 'fill'
  /**
   * 圆角大小
   */
  borderRadius?: number | string
  /**
   * 自定义默认图片URL，如果不传则使用全局默认图片
   */
  defaultSrc?: string
  /**
   * 是否启用默认图片功能，默认为true
   */
  enableDefaultImage?: boolean
  /**
   * 是否在加载时也显示默认图片作为占位符，默认为true
   */
  showDefaultOnLoading?: boolean
  /**
   * 加载失败时的回调
   */
  onError?: () => void
}

/**
 * 图片源类型定义 - 兼容 Next.js ImageProps['src'] 类型
 */
type ImageSource = ImageProps['src']

/**
 * 获取图片源字符串
 */
const getImageSrc = (src: ImageSource): string => {
  if (typeof src === 'string') {
    return src
  }
  if (src && typeof src === 'object' && 'src' in src) {
    return src.src
  }
  return ''
}

/**
 * 自定义图片组件，对Next.js Image组件进行封装，提供更多功能
 */
const CustomImage = ({
  src,
  alt = '',
  width,
  height,
  displayMode = 'fixed',
  aspectRatio = '1/1',
  containerClassName = '',
  className = '',
  showPlaceholder = true,
  placeholderColor = '#f5f5f5',
  objectFit = 'cover',
  borderRadius = 0,
  defaultSrc = DEFAULT_PLACEHOLDER.src,
  enableDefaultImage = true,
  showDefaultOnLoading = true,
  onError,
  priority = false,
  ...rest
}: CustomImageProps) => {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [currentSrc, setCurrentSrc] = useState<string>(getImageSrc(src))

  const handleImageLoad = () => {
    setIsLoading(false)
  }

  const handleImageError = useCallback(() => {
    setIsLoading(false)
    // 如果启用默认图片功能且当前不是默认图片
    if (enableDefaultImage && currentSrc !== defaultSrc) {
      setCurrentSrc(defaultSrc)
      // 不设置hasError为true，让默认图片能正常显示
    } else {
      // 只有在禁用默认图片功能或默认图片也加载失败时才设置错误状态
      setHasError(true)
    }
    onError?.()
  }, [enableDefaultImage, currentSrc, defaultSrc, onError])

  // 当src属性变化时，重置状态
  useEffect(() => {
    const newSrc = getImageSrc(src)
    if (newSrc !== currentSrc) {
      setCurrentSrc(newSrc)
      setHasError(false)
      setIsLoading(true)
    }
  }, [src, currentSrc])

  // 样式计算
  const containerStyle: React.CSSProperties = {
    position: 'relative',
    overflow: 'hidden',
    borderRadius: borderRadius ? borderRadius : undefined,
    backgroundColor: (isLoading || hasError) && showPlaceholder ? placeholderColor : undefined,
  }

  // 根据显示模式添加不同样式
  if (displayMode === 'responsive') {
    containerStyle.width = '100%'
    containerStyle.paddingBottom = aspectRatio
      ? `calc((${aspectRatio.split('/')[1]} / ${aspectRatio.split('/')[0]}) * 100%)`
      : '100%'
  } else if (displayMode === 'fixed') {
    containerStyle.width = typeof width === 'number' ? `${width}px` : width
    containerStyle.height = typeof height === 'number' ? `${height}px` : height
  }

  // 图片样式
  const imageStyle: React.CSSProperties = {
    objectFit,
  }

  return (
    <div className={containerClassName} style={containerStyle}>
      {(enableDefaultImage || !hasError) && (
        <Image
          src={currentSrc}
          alt={alt}
          className={clsx('transition-opacity duration-300', className, {
            'opacity-0': isLoading,
            'opacity-100': !isLoading,
          })}
          fill={displayMode === 'fill' || displayMode === 'responsive'}
          width={displayMode === 'fixed' ? width : undefined}
          height={displayMode === 'fixed' ? height : undefined}
          onLoad={handleImageLoad}
          onError={handleImageError}
          style={imageStyle}
          sizes={
            displayMode === 'responsive'
              ? '100vw'
              : displayMode === 'fill'
                ? '(max-width: 768px) 100vw, 50vw'
                : undefined
          }
          {...rest}
          priority={priority}
        />
      )}

      {/* 加载时的默认图片占位符 */}
      {isLoading && enableDefaultImage && showDefaultOnLoading && (
        <div className="absolute inset-0">
          <Image
            src={defaultSrc}
            alt="加载中..."
            className="opacity-50"
            fill={displayMode === 'fill' || displayMode === 'responsive'}
            width={displayMode === 'fixed' ? width : undefined}
            height={displayMode === 'fixed' ? height : undefined}
            style={imageStyle}
            sizes={
              displayMode === 'responsive'
                ? '100vw'
                : displayMode === 'fill'
                  ? '(max-width: 768px) 100vw, 50vw'
                  : undefined
            }
          />
        </div>
      )}

      {hasError && !enableDefaultImage && (
        <div
          className="absolute inset-0 flex items-center justify-center text-gray-400"
          style={{ backgroundColor: placeholderColor }}>
          图片加载失败
        </div>
      )}
    </div>
  )
}

export default CustomImage
