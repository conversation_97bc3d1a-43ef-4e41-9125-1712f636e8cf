import Image from 'next/image'
import { generateEventParams, TRACK_EVENT, useVolcAnalytics } from '@ninebot/core'
import { Button } from 'antd'

import { IconArrow } from '@/components'
import { Link } from '@/i18n/navigation'
interface BannerSectionProps {
  subtitle?: string
  title?: string
  description?: string
  buttonText?: string
  buttonArrow?: boolean
  landingPageUrl:
    | {
        __typename?: 'UrlPath'
        type?: string | null
        url?: string | null
        value?: string | null
      }
    | null
    | undefined
  isVisible?: boolean
  backgroundImage?: string
}

const BannerSection = ({
  subtitle,
  title,
  description,
  buttonText,
  buttonArrow = false,
  landingPageUrl,
  isVisible = true,
  backgroundImage,
}: BannerSectionProps) => {
  const { reportEvent } = useVolcAnalytics()
  if (!isVisible) return null

  return (
    <Link
      className="mb-base-16 block"
      onClick={() => {
        if (landingPageUrl) {
          reportEvent(
            TRACK_EVENT.shop_homepage_banner_picture_click,
            generateEventParams({ ...landingPageUrl, label: title }),
          )
        }
      }}
      href={landingPageUrl?.url || 'javascript:void(0)'}>
      <div className="responsive-module-banner relative w-full overflow-hidden rounded-[20px] bg-black">
        <div className="absolute left-[10%] top-[15%] z-20 text-white 2xl:left-[14.24%] 2xl:top-[19.44%]">
          {subtitle && (
            <div className="mb-base font-miSansMedium380 text-[18px] leading-[120%] 2xl:text-[20px]">
              {subtitle}
            </div>
          )}

          {title && (
            <div className="mb-base font-miSansSemibold520 text-[40px] leading-[120%] 2xl:text-[64px]">
              {title}
            </div>
          )}

          {description && (
            <div className="font-miSansMedium380 text-[16px] leading-[140%] 2xl:text-[24px]">
              {description}
            </div>
          )}

          {buttonText && (
            <Button type="primary" className="mt-[38px] 2xl:mt-base-48">
              <div className="flex items-center gap-24 font-miSansDemiBold450 text-[13px] leading-[16px] 2xl:text-[16px] 2xl:leading-[20px]">
                {buttonText}
                {buttonArrow ? <IconArrow color="#fff" rotate={-90} size={20} /> : ''}
              </div>
            </Button>
          )}
        </div>

        <div className="bg-[#1a1a1a]">
          {backgroundImage && (
            <Image src={backgroundImage} alt={title || ''} fill className="object-cover" priority />
          )}
        </div>
      </div>
    </Link>
  )
}

export default BannerSection
