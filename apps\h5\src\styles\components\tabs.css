/* Tabs 组件自定义样式 */
.custom-tabs {
  .adm-tabs-header {
    border-bottom: none;
  }

  .adm-tabs-tab-active {
    color: #da291c;
  }

  .adm-tabs-tab-line {
    display: none;
  }

  .adm-tabs-header-mask-right {
    background: linear-gradient(
      to left,
      #ffffff 15%,
      /* 完全不透明的白色起点 */ rgba(255, 255, 255, 0.95) 30%,
      /* 高透明度过渡 */ rgba(255, 255, 255, 0.6) 50%,
      /* 中等透明度过渡 */ rgba(255, 255, 255, 0) 100% /* 完全透明终点 */
    );
    width: 60px; /* 增加遮罩宽度 */
  }

  .adm-tabs-header-mask-left {
    background: linear-gradient(
      to right,
      #ffffff 15%,
      /* 完全不透明的白色起点 */ rgba(255, 255, 255, 0.95) 30%,
      /* 高透明度过渡 */ rgba(255, 255, 255, 0.6) 50%,
      /* 中等透明度过渡 */ rgba(255, 255, 255, 0) 100% /* 完全透明终点 */
    );
    width: 60px; /* 增加遮罩宽度 */
  }
}

/* 单个Tab项的样式 */
.custom-tab-item {
  --title-font-size: 14px;
}

/* 修改 CapsuleTabs 样式 */
.adm-capsule-tabs {
  --adm-color-text-light-solid: #da291c;
  --adm-capsule-tabs-border-radius: 20px;
  --adm-color-primary: #da291c0d;
}
.adm-capsule-tabs-tab-active {
  border: 1px solid #da291c;
}

.adm-capsule-tabs-tab {
  padding: 8px 16px;
}

/* 优惠券tabs */
.coupon-tabs {
  .adm-tabs-header {
    margin: 0 2.4rem;
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #ffffff;
    border-bottom: none;
  }
  .adm-tabs-tab-list {
    gap: 3.2rem;
    .adm-tabs-tab-line {
      display: none;
    }
    .adm-tabs-tab-wrapper {
      padding: 0;
      .adm-tabs-tab {
        font-size: 1.4rem;
        line-height: 1.4;
        &.adm-tabs-tab-active {
          font-family: var(--font-family-miSansDemiBold450);
          font-size: 1.6rem;
        }
      }
    }
  }
}

/* 订单列表tabs */
.order-tabs {
  .adm-tabs-header {
    padding: 0;
    border: none;
    .adm-tabs-header-mask {
      display: none;
    }

    .adm-tabs-tab-wrapper {
      padding: 0;
      padding-right: 2.4rem;
      .adm-tabs-tab {
        font-size: 1.4rem;
        padding: 1.2rem 0;
      }
    }
  }
}

/* 透明状态下的Tabs样式 */
.tabs-transparent {
  .adm-tabs-header {
    border-bottom: none;
  }

  .adm-tabs-tab {
    color: white;
  }

  .adm-tabs-tab-active {
    color: white;
  }

  .adm-tabs-tab-line {
    background: white;
  }

  .adm-tabs-header-mask-right {
    background: linear-gradient(
      to left,
      rgba(255, 255, 255, 0.2) 15%,
      /* 略微不透明的起点 */ rgba(255, 255, 255, 0.15) 30%,
      /* 高透明度过渡 */ rgba(255, 255, 255, 0.1) 50%,
      /* 中等透明度过渡 */ rgba(255, 255, 255, 0) 100% /* 完全透明终点 */
    );
  }

  .adm-tabs-header-mask-left {
    background: linear-gradient(
      to right,
      rgba(255, 255, 255, 0.2) 15%,
      /* 略微不透明的起点 */ rgba(255, 255, 255, 0.15) 30%,
      /* 高透明度过渡 */ rgba(255, 255, 255, 0.1) 50%,
      /* 中等透明度过渡 */ rgba(255, 255, 255, 0) 100% /* 完全透明终点 */
    );
  }
}

/* 添加过渡动画 */
.adm-tabs-tab {
  transition: color 0.3s ease;
}

.adm-tabs-tab-line {
  transition: background-color 0.3s ease;
}

/* Header Tabs样式 */
.header-tabs {
  --title-font-size: 16px;

  .adm-tabs-header {
    border-bottom: none;
  }

  .adm-tabs-header-mask {
    display: none;
  }

  .adm-tabs-tab-active {
    --title-font-size: 20px;
  }

  .adm-tabs-tab-line {
    height: 1px;
  }
}

/* 透明状态下的Header Tabs样式 */
.header-tabs-transparent {
  .adm-tabs-tab {
    color: rgba(255, 255, 255, 0.85);
  }

  .adm-tabs-tab-line {
    background: white;
  }
}

/* Header Tab项样式 */
.header-tab-item {
  transition: color 0.3s ease;
  .adm-tabs-tab {
    font-family: var(--font-family-miSansMedium380);
    font-size: 1.6rem;
    line-height: 1.4;
    letter-spacing: 0;
    padding: 0.8rem 0;
  }
}

.header-tab-item-active {
  .adm-tabs-tab {
    font-family: var(--font-family-miSansDemiBold450);
    font-size: 2rem;
    line-height: 1.2;
    text-align: center;
    letter-spacing: 0;
    padding: 0.8rem 0;
  }
}

.header-tab-item,
.header-tab-item-active {
  flex: unset !important;
  padding: 0 !important;
  margin-right: 16px;
  .adm-tabs-tab {
    margin: 0;
    padding-left: 0;
    padding-right: 0;
  }
}

/* item left 样式 */
.items-left {
  .adm-tabs-header {
    .adm-tabs-header-mask {
      display: none;
    }

    .adm-tabs-tab-list {
      display: flex;
      align-items: baseline;
      line-height: 1.4;
      .custom-tab-item {
        flex: unset;
        padding: 0;
        margin-right: 24px;
        .adm-tabs-tab {
          padding: 0;
          margin: 0;
        }
      }
      .custom-tab-item:not(.home-tab) {
        .adm-tabs-tab-active {
          font-size: 16px;
          font-family: var(--font-family-miSansDemiBold450);
        }
      }

      .address-select-tab {
        flex: unset;
        padding: 0;
        margin-right: 24px;
        .adm-tabs-tab {
          padding: 0;
          margin: 0;
          padding-bottom: 12px;
        }
      }
    }
  }
}

.home-tab-item {
  .adm-tabs-tab,
  .adm-tabs-tab-active {
    color: black;
  }
}

.other-tab-item {
  .adm-tabs-tab {
    color: #6e6e73;
  }
  .adm-tabs-tab-active {
    color: #000000;
  }
}
/* pdp-address-tabs */
.pdp-address-tabs {
  .adm-tabs-tab {
    color: #6e6e73;
  }

  .adm-tabs-tab-active {
    color: #000000;
  }
}

.cart-tabs {
  .cart-tab-item {
    .adm-tabs-tab {
      padding: 11px 0;
    }
  }
}

/* 发票tabs */
.invoice-tabs {
  .adm-tabs-header {
    border: none;
  }
  .adm-tabs-tab-wrapper {
    padding: 0;
  }
  .adm-tabs-tab {
    color: #6e6e73;
    font-size: 14px;
    line-height: 21px;
    padding: 32px 16px;
    margin: 0;
    &.adm-tabs-tab-active {
      font-family: var(--font-family-miSansDemiBold450);
      color: #000;
      font-size: 16px;
    }
  }
}
